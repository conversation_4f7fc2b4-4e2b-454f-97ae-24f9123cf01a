import { Consts, Common } from "@commune/api";
import { FileInterceptor } from "@nestjs/platform-express";
import {
    Controller,
    Delete,
    FileTypeValidator,
    MaxFileSizeValidator,
    NotFoundException,
    Param,
    ParseFilePipe,
    Post,
    Put,
    UploadedFile,
    UseGuards,
    UseInterceptors,
} from "@nestjs/common";
import { ZodPipe } from "src/zod";
import { getServer } from "src/acrpc";
import { CurrentUser } from "src/auth/types";
import { HttpCurrentUser } from "src/auth/http/current-user.decorator";
import { HttpSessionAuthGuard } from "src/auth/http/session-auth.guard";
import { ReactorHubService } from "./reactor-hub.service";
import { ReactorPostService } from "./reactor-post.service";
import { ReactorLensService } from "./lens/reactor-lens.service";
import { ReactorCommentService } from "./reactor-comment.service";
import { ReactorCommunityService } from "./reactor-community.service";

@Controller("reactor")
@UseGuards(HttpSessionAuthGuard)
export class ReactorController {
    constructor(
        private readonly reactorPostService: ReactorPostService,
        private readonly reactorCommentService: ReactorCommentService,
        private readonly reactorLensService: ReactorLensService,
        private readonly reactorHubService: ReactorHubService,
        private readonly reactorCommunityService: ReactorCommunityService,
    ) {
        const acrpcServer = getServer();

        acrpcServer.register({
            reactor: {
                post: {
                    list: {
                        get: (input, metadata) =>
                            this.reactorPostService.getPosts(
                                input,
                                metadata.user,
                            ),
                    },
                    post: (input, metadata) =>
                        this.reactorPostService.createPost(
                            input,
                            metadata.user,
                        ),
                    patch: (input, metadata) =>
                        this.reactorPostService.updatePost(
                            input,
                            metadata.user,
                        ),
                    delete: (input, metadata) =>
                        this.reactorPostService.deletePost(
                            input,
                            metadata.user,
                        ),
                    rating: {
                        post: (input, metadata) =>
                            this.reactorPostService.updatePostRating(
                                input,
                                metadata.user,
                            ),
                    },
                    usefulness: {
                        post: (input, metadata) =>
                            this.reactorPostService.updatePostUsefulness(
                                input,
                                metadata.user,
                            ),
                    },
                },
                comment: {
                    list: {
                        get: (input, metadata) =>
                            this.reactorCommentService.getComments(
                                input,
                                metadata.user,
                            ),
                    },
                    post: (input, metadata) =>
                        this.reactorCommentService.createComment(
                            input,
                            metadata.user,
                        ),
                    patch: (input, metadata) =>
                        this.reactorCommentService.updateComment(
                            input,
                            metadata.user,
                        ),
                    delete: (input, metadata) =>
                        this.reactorCommentService.deleteComment(
                            input,
                            metadata.user,
                        ),
                    rating: {
                        post: (input, metadata) =>
                            this.reactorCommentService.updateCommentRating(
                                input,
                                metadata.user,
                            ),
                    },
                    anonimify: {
                        post: (input, metadata) =>
                            this.reactorCommentService.anonimifyComment(
                                input,
                                metadata.user,
                            ),
                    },
                },
                lens: {
                    list: {
                        get: (_, metadata) =>
                            this.reactorLensService.getLenses(metadata.user),
                    },
                    post: (input, metadata) =>
                        this.reactorLensService.createLens(
                            input,
                            metadata.user,
                        ),
                    patch: (input, metadata) =>
                        this.reactorLensService.updateLens(
                            input,
                            metadata.user,
                        ),
                    delete: (input, metadata) =>
                        this.reactorLensService.deleteLens(
                            input,
                            metadata.user,
                        ),
                },
                hub: {
                    list: {
                        get: async (input, metadata) => {
                            const hubs = await this.reactorHubService.getHubs(
                                input,
                                metadata.user,
                            );

                            return hubs.map((hub) => ({
                                ...hub,
                                headUser: {
                                    ...hub.headUser,
                                    image: hub.headUser.image?.url ?? null,
                                },
                                image: hub.image?.url ?? null,
                            }));
                        },
                    },
                    post: (input, metadata) =>
                        this.reactorHubService.createHub(input, metadata.user),
                    patch: (input, metadata) =>
                        this.reactorHubService.updateHub(input, metadata.user),
                },
                community: {
                    list: {
                        get: async (input, metadata) => {
                            const communities =
                                await this.reactorCommunityService.getCommunities(
                                    input,
                                    metadata.user,
                                );

                            return communities.map((community) => ({
                                ...community,
                                hub: community.hub
                                    ? {
                                          ...community.hub,
                                          image:
                                              community.hub.image?.url ?? null,
                                      }
                                    : null,
                                headUser: {
                                    ...community.headUser,
                                    image:
                                        community.headUser.image?.url ?? null,
                                },
                                image: community.image?.url ?? null,
                            }));
                        },
                    },
                    post: (input, metadata) =>
                        this.reactorCommunityService.createCommunity(
                            input,
                            metadata.user,
                        ),
                    patch: (input, metadata) =>
                        this.reactorCommunityService.updateCommunity(
                            input,
                            metadata.user,
                        ),
                },
            },
        });
    }

    // posts

    @Post("__mock_post__")
    async createMockPost(@HttpCurrentUser() user: CurrentUser) {
        if (!user.isAdmin) {
            throw new NotFoundException();
        }

        return await this.reactorPostService.createMockPost();
    }

    // @Get("post")
    // async getPosts(
    //     @Query(new ZodPipe(Common.PaginationSchema))
    //     pagination: Common.Pagination,
    //     @Query(new ZodPipe(Reactor.GetPostsInputSchema))
    //     input: Reactor.GetPostsInput,
    //     @HttpCurrentUser() user: CurrentUser,
    // ) {
    //     const posts = await this.reactorPostService.getPosts(
    //         input,
    //         pagination,
    //         user,
    //     );

    //     return Common.parseInput(Reactor.GetPostsOutputSchema, posts);
    // }

    // @Get("post/:id")
    // async getPost(
    //     @Param("id", new ZodPipe(Common.id)) id: string,
    //     @HttpCurrentUser() user: CurrentUser,
    // ) {
    //     const post = await this.reactorPostService.getPost(id, user);

    //     if (!post) {
    //         throw new NotFoundException(...getError("post"));
    //     }

    //     return Common.parseInput(Reactor.GetPostOutputSchema, post);
    // }

    // @Post("post")
    // async createPost(
    //     @Body(new ZodPipe(Reactor.CreatePostInputSchema))
    //     input: Reactor.CreatePostInput,
    //     @HttpCurrentUser() user: CurrentUser,
    // ) {
    //     return await this.reactorPostService.createPost(input, user);
    // }

    // @Patch("post/:id")
    // async updatePost(
    //     @Param("id", new ZodPipe(Common.id)) id: string,
    //     @Body(new ZodPipe(Reactor.UpdatePostInputSchema))
    //     input: Reactor.UpdatePostInput,
    //     @HttpCurrentUser() user: CurrentUser,
    // ) {
    //     return await this.reactorPostService.updatePost(id, input, user);
    // }

    // @Post("post/:id/rating")
    // async updatePostRating(
    //     @Param("id", new ZodPipe(Common.id)) id: string,
    //     @Body(new ZodPipe(Reactor.UpdatePostRatingInputSchema))
    //     input: Reactor.UpdatePostRatingInput,
    //     @HttpCurrentUser() user: CurrentUser,
    // ) {
    //     const newRating = await this.reactorPostService.updatePostRating(
    //         id,
    //         input,
    //         user,
    //     );

    //     return Common.parseInput(
    //         Reactor.UpdatePostRatingOutputSchema,
    //         newRating,
    //     );
    // }

    // @Post("post/:id/usefulness")
    // async updatePostUsefulness(
    //     @Param("id", new ZodPipe(Common.id)) id: string,
    //     @Body(new ZodPipe(Reactor.UpdatePostUsefulnessInputSchema))
    //     input: Reactor.UpdatePostUsefulnessInput,
    //     @HttpCurrentUser() user: CurrentUser,
    // ) {
    //     const newUsefulness =
    //         await this.reactorPostService.updatePostUsefulness(id, input, user);

    //     return Common.parseInput(
    //         Reactor.UpdatePostUsefulnessOutputSchema,
    //         newUsefulness,
    //     );
    // }

    // @Delete("post/:id")
    // async deletePost(
    //     @Param("id", new ZodPipe(Common.id)) id: string,
    //     @Body(new ZodPipe(Reactor.DeletePostInputSchema))
    //     input: Reactor.DeletePostInput,
    //     @HttpCurrentUser() user: CurrentUser,
    // ) {
    //     return await this.reactorPostService.deletePost(id, input, user);
    // }

    // comments

    // @Get("comment")
    // async getComments(
    //     @Query(new ZodPipe(Reactor.GetCommentsInputSchema))
    //     input: Reactor.GetCommentsInput,
    //     @HttpCurrentUser() user: CurrentUser,
    // ) {
    //     const comments = await this.reactorCommentService.getComments(
    //         input,
    //         user,
    //     );

    //     return Common.parseInput(Reactor.GetCommentsOutputSchema, comments);
    // }

    // @Post("comment")
    // async createComment(
    //     @Body(new ZodPipe(Reactor.CreateCommentInputSchema))
    //     input: Reactor.CreateCommentInput,
    //     @HttpCurrentUser() user: CurrentUser,
    // ) {
    //     return await this.reactorCommentService.createComment(input, user);
    // }

    // @Patch("comment/:id")
    // async updateComment(
    //     @Param("id", new ZodPipe(Common.id)) id: string,
    //     @Body(new ZodPipe(Reactor.UpdateCommentInputSchema))
    //     input: Reactor.UpdateCommentInput,
    //     @HttpCurrentUser() user: CurrentUser,
    // ) {
    //     return await this.reactorCommentService.updateComment(id, input, user);
    // }

    // @Post("comment/rating")
    // async updateCommentRating(
    //     @Body(new ZodPipe(Reactor.UpdateCommentRatingInputSchema))
    //     input: Reactor.UpdateCommentRatingInput,
    //     @HttpCurrentUser() user: CurrentUser,
    // ) {
    //     const newRating = await this.reactorCommentService.updateCommentRating(
    //         input,
    //         user,
    //     );

    //     return Common.parseInput(
    //         Reactor.UpdateCommentRatingOutputSchema,
    //         newRating,
    //     );
    // }

    // @Put("comment/anonimify")
    // async anonimifyComment(
    //     @Body(new ZodPipe(Reactor.AnonimifyCommentInputSchema))
    //     input: Reactor.AnonimifyCommentInput,
    //     @HttpCurrentUser() user: CurrentUser,
    // ) {
    //     return await this.reactorCommentService.anonimifyComment(input, user);
    // }

    // @Delete("comment/:id")
    // async deleteComment(
    //     @Param("id", new ZodPipe(Common.id)) id: string,
    //     @Body(new ZodPipe(Reactor.DeleteCommentInputSchema))
    //     input: Reactor.DeleteCommentInput,
    //     @HttpCurrentUser() user: CurrentUser,
    // ) {
    //     return await this.reactorCommentService.deleteComment(id, input, user);
    // }

    // lenses

    // @Get("lens")
    // async getLenses(@HttpCurrentUser() user: CurrentUser) {
    //     return await this.reactorLensService.getLenses(user);
    // }

    // @Post("lens")
    // async createLens(
    //     @Body(new ZodPipe(Reactor.CreateLensInputSchema))
    //     input: Reactor.CreateLensInput,
    //     @HttpCurrentUser() user: CurrentUser,
    // ) {
    //     return await this.reactorLensService.createLens(input, user);
    // }

    // @Put("lens/:id")
    // async updateLens(
    //     @Param("id", new ZodPipe(Common.id)) id: string,
    //     @Body(new ZodPipe(Reactor.UpdateLensInputSchema))
    //     input: Reactor.UpdateLensInput,
    //     @HttpCurrentUser() user: CurrentUser,
    // ) {
    //     return await this.reactorLensService.updateLens(id, input, user);
    // }

    // @Delete("lens/:id")
    // async deleteLens(
    //     @Param("id", new ZodPipe(Common.id)) id: string,
    //     @HttpCurrentUser() user: CurrentUser,
    // ) {
    //     return await this.reactorLensService.deleteLens(id, user);
    // }

    // hubs

    // @Get("hub")
    // async getHubs(
    //     @Query(new ZodPipe(Common.PaginationSchema))
    //     pagination: Common.Pagination,
    //     @Query(
    //         "body",
    //         new ZodPipe(
    //             Common.JsonStringToObject(Reactor.GetHubsInputSchema.shape),
    //         ),
    //     )
    //     body: Reactor.GetHubsInput,
    //     @HttpCurrentUser() user: CurrentUser,
    // ) {
    //     const hubs = await this.reactorHubService.getHubs(
    //         {
    //             input: body,
    //             pagination,
    //         },
    //         user,
    //     );

    //     return Common.parseInput(
    //         Reactor.GetHubsOutputSchema,
    //         hubs.map((hub) => ({
    //             ...hub,
    //             headUser: {
    //                 ...hub.headUser,
    //                 image: hub.headUser.image?.url ?? null,
    //             },
    //             image: hub.image?.url ?? null,
    //         })),
    //     );
    // }

    // @Post("hub")
    // async createHub(
    //     @Body(new ZodPipe(Reactor.CreateHubInputSchema))
    //     body: Reactor.CreateHubInput,
    //     @HttpCurrentUser() user: CurrentUser,
    // ) {
    //     const hub = await this.reactorHubService.createHub(body, user);

    //     return Common.parseInput(Common.ObjectWithIdSchema, hub);
    // }

    // @Put("hub/:id")
    // async updateHub(
    //     @Param("id", new ZodPipe(Common.id)) id: string,
    //     @Body(new ZodPipe(Reactor.UpdateHubInputSchema))
    //     body: Reactor.UpdateHubInput,
    //     @HttpCurrentUser() user: CurrentUser,
    // ) {
    //     await this.reactorHubService.updateHub(id, body, user);
    // }

    @Put("hub/:id/image")
    @UseInterceptors(FileInterceptor("image"))
    async updateHubImage(
        @Param("id", new ZodPipe(Common.id)) id: string,
        @HttpCurrentUser() user: CurrentUser,

        @UploadedFile(
            new ParseFilePipe({
                validators: [
                    new MaxFileSizeValidator({
                        maxSize: Consts.MAX_IMAGE_FILE_SIZE,
                    }),
                    new FileTypeValidator({
                        fileType: Consts.ALLOWED_IMAGE_FILE_TYPES.join("|"),
                    }),
                ],
            }),
        )
        file: Express.Multer.File,
    ) {
        await this.reactorHubService.updateHubImage(id, file, user);
    }

    @Delete("hub/:id/image")
    async deleteHubImage(
        @Param("id", new ZodPipe(Common.id)) id: string,
        @HttpCurrentUser() user: CurrentUser,
    ) {
        await this.reactorHubService.deleteHubImage(id, user);
    }

    // communities

    // @Get("community")
    // @HttpCode(HttpStatus.OK)
    // async getCommunities(
    //     @Query(new ZodPipe(Common.PaginationSchema))
    //     pagination: Common.Pagination,
    //     @Query(
    //         "body",
    //         new ZodPipe(
    //             Common.JsonStringToObject(
    //                 Reactor.GetCommunitiesInputSchema.shape,
    //             ),
    //         ),
    //     )
    //     body: Reactor.GetCommunitiesInput,
    //     @HttpCurrentUser() user: CurrentUser,
    // ) {
    //     const communities = await this.reactorCommunityService.getCommunities(
    //         {
    //             input: body,
    //             pagination,
    //         },
    //         user,
    //     );

    //     return Common.parseInput(
    //         Reactor.GetCommunitiesOutputSchema,
    //         communities.map((community) => ({
    //             ...community,
    //             hub: community.hub
    //                 ? {
    //                       ...community.hub,
    //                       image: community.hub.image?.url ?? null,
    //                   }
    //                 : null,
    //             headUser: {
    //                 ...community.headUser,
    //                 image: community.headUser.image?.url ?? null,
    //             },
    //             image: community.image?.url ?? null,
    //         })),
    //     );
    // }

    // @Post("community")
    // async createCommunity(
    //     @Body(new ZodPipe(Reactor.CreateCommunityInputSchema))
    //     body: Reactor.CreateCommunityInput,
    //     @HttpCurrentUser() user: CurrentUser,
    // ) {
    //     const community = await this.reactorCommunityService.createCommunity(
    //         body,
    //         user,
    //     );

    //     return Common.parseInput(Common.ObjectWithIdSchema, community);
    // }

    // @Put("community/:id")
    // async updateCommunity(
    //     @Param("id", new ZodPipe(Common.id)) id: string,
    //     @Body(new ZodPipe(Reactor.UpdateCommunityInputSchema))
    //     body: Reactor.UpdateCommunityInput,
    //     @HttpCurrentUser() user: CurrentUser,
    // ) {
    //     await this.reactorCommunityService.updateCommunity(id, body, user);
    // }

    @Put("community/:id/image")
    @UseInterceptors(FileInterceptor("image"))
    async updateCommunityImage(
        @Param("id", new ZodPipe(Common.id)) id: string,
        @HttpCurrentUser() user: CurrentUser,

        @UploadedFile(
            new ParseFilePipe({
                validators: [
                    new MaxFileSizeValidator({
                        maxSize: Consts.MAX_IMAGE_FILE_SIZE,
                    }),
                    new FileTypeValidator({
                        fileType: Consts.ALLOWED_IMAGE_FILE_TYPES.join("|"),
                    }),
                ],
            }),
        )
        file: Express.Multer.File,
    ) {
        await this.reactorCommunityService.updateCommunityImage(id, file, user);
    }

    @Delete("community/:id/image")
    async deleteCommunityImage(
        @Param("id", new ZodPipe(Common.id)) id: string,
        @HttpCurrentUser() user: CurrentUser,
    ) {
        await this.reactorCommunityService.deleteCommunityImage(id, user);
    }
}
