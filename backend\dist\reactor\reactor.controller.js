"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ReactorController = void 0;
const api_1 = require("@commune/api");
const platform_express_1 = require("@nestjs/platform-express");
const common_1 = require("@nestjs/common");
const zod_1 = require("../zod");
const acrpc_1 = require("../acrpc");
const current_user_decorator_1 = require("../auth/http/current-user.decorator");
const session_auth_guard_1 = require("../auth/http/session-auth.guard");
const reactor_hub_service_1 = require("./reactor-hub.service");
const reactor_post_service_1 = require("./reactor-post.service");
const reactor_lens_service_1 = require("./lens/reactor-lens.service");
const reactor_comment_service_1 = require("./reactor-comment.service");
const reactor_community_service_1 = require("./reactor-community.service");
let ReactorController = class ReactorController {
    constructor(reactorPostService, reactorCommentService, reactorLensService, reactorHubService, reactorCommunityService) {
        this.reactorPostService = reactorPostService;
        this.reactorCommentService = reactorCommentService;
        this.reactorLensService = reactorLensService;
        this.reactorHubService = reactorHubService;
        this.reactorCommunityService = reactorCommunityService;
        const acrpcServer = (0, acrpc_1.getServer)();
        acrpcServer.register({
            reactor: {
                post: {
                    list: {
                        get: (input, metadata) => this.reactorPostService.getPosts(input, metadata.user),
                    },
                    post: (input, metadata) => this.reactorPostService.createPost(input, metadata.user),
                    patch: (input, metadata) => this.reactorPostService.updatePost(input, metadata.user),
                    delete: (input, metadata) => this.reactorPostService.deletePost(input, metadata.user),
                    rating: {
                        post: (input, metadata) => this.reactorPostService.updatePostRating(input, metadata.user),
                    },
                    usefulness: {
                        post: (input, metadata) => this.reactorPostService.updatePostUsefulness(input, metadata.user),
                    },
                },
                comment: {
                    list: {
                        get: (input, metadata) => this.reactorCommentService.getComments(input, metadata.user),
                    },
                    post: (input, metadata) => this.reactorCommentService.createComment(input, metadata.user),
                    patch: (input, metadata) => this.reactorCommentService.updateComment(input, metadata.user),
                    delete: (input, metadata) => this.reactorCommentService.deleteComment(input, metadata.user),
                    rating: {
                        post: (input, metadata) => this.reactorCommentService.updateCommentRating(input, metadata.user),
                    },
                    anonimify: {
                        post: (input, metadata) => this.reactorCommentService.anonimifyComment(input, metadata.user),
                    },
                },
                lens: {
                    list: {
                        get: (_, metadata) => this.reactorLensService.getLenses(metadata.user),
                    },
                    post: (input, metadata) => this.reactorLensService.createLens(input, metadata.user),
                    patch: (input, metadata) => this.reactorLensService.updateLens(input, metadata.user),
                    delete: (input, metadata) => this.reactorLensService.deleteLens(input, metadata.user),
                },
                hub: {
                    list: {
                        get: async (input, metadata) => {
                            const hubs = await this.reactorHubService.getHubs(input, metadata.user);
                            return hubs.map((hub) => ({
                                ...hub,
                                headUser: {
                                    ...hub.headUser,
                                    image: hub.headUser.image?.url ?? null,
                                },
                                image: hub.image?.url ?? null,
                            }));
                        },
                    },
                    post: (input, metadata) => this.reactorHubService.createHub(input, metadata.user),
                    patch: (input, metadata) => this.reactorHubService.updateHub(input, metadata.user),
                },
                community: {
                    list: {
                        get: async (input, metadata) => {
                            const communities = await this.reactorCommunityService.getCommunities(input, metadata.user);
                            return communities.map((community) => ({
                                ...community,
                                hub: community.hub
                                    ? {
                                        ...community.hub,
                                        image: community.hub.image?.url ?? null,
                                    }
                                    : null,
                                headUser: {
                                    ...community.headUser,
                                    image: community.headUser.image?.url ?? null,
                                },
                                image: community.image?.url ?? null,
                            }));
                        },
                    },
                    post: (input, metadata) => this.reactorCommunityService.createCommunity(input, metadata.user),
                    patch: (input, metadata) => this.reactorCommunityService.updateCommunity(input, metadata.user),
                },
            },
        });
    }
    async createMockPost(user) {
        if (!user.isAdmin) {
            throw new common_1.NotFoundException();
        }
        return await this.reactorPostService.createMockPost();
    }
    async updateHubImage(id, user, file) {
        await this.reactorHubService.updateHubImage(id, file, user);
    }
    async deleteHubImage(id, user) {
        await this.reactorHubService.deleteHubImage(id, user);
    }
    async updateCommunityImage(id, user, file) {
        await this.reactorCommunityService.updateCommunityImage(id, file, user);
    }
    async deleteCommunityImage(id, user) {
        await this.reactorCommunityService.deleteCommunityImage(id, user);
    }
};
exports.ReactorController = ReactorController;
__decorate([
    (0, common_1.Post)("__mock_post__"),
    __param(0, (0, current_user_decorator_1.HttpCurrentUser)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], ReactorController.prototype, "createMockPost", null);
__decorate([
    (0, common_1.Put)("hub/:id/image"),
    (0, common_1.UseInterceptors)((0, platform_express_1.FileInterceptor)("image")),
    __param(0, (0, common_1.Param)("id", new zod_1.ZodPipe(api_1.Common.id))),
    __param(1, (0, current_user_decorator_1.HttpCurrentUser)()),
    __param(2, (0, common_1.UploadedFile)(new common_1.ParseFilePipe({
        validators: [
            new common_1.MaxFileSizeValidator({
                maxSize: api_1.Consts.MAX_IMAGE_FILE_SIZE,
            }),
            new common_1.FileTypeValidator({
                fileType: api_1.Consts.ALLOWED_IMAGE_FILE_TYPES.join("|"),
            }),
        ],
    }))),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object, Object]),
    __metadata("design:returntype", Promise)
], ReactorController.prototype, "updateHubImage", null);
__decorate([
    (0, common_1.Delete)("hub/:id/image"),
    __param(0, (0, common_1.Param)("id", new zod_1.ZodPipe(api_1.Common.id))),
    __param(1, (0, current_user_decorator_1.HttpCurrentUser)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object]),
    __metadata("design:returntype", Promise)
], ReactorController.prototype, "deleteHubImage", null);
__decorate([
    (0, common_1.Put)("community/:id/image"),
    (0, common_1.UseInterceptors)((0, platform_express_1.FileInterceptor)("image")),
    __param(0, (0, common_1.Param)("id", new zod_1.ZodPipe(api_1.Common.id))),
    __param(1, (0, current_user_decorator_1.HttpCurrentUser)()),
    __param(2, (0, common_1.UploadedFile)(new common_1.ParseFilePipe({
        validators: [
            new common_1.MaxFileSizeValidator({
                maxSize: api_1.Consts.MAX_IMAGE_FILE_SIZE,
            }),
            new common_1.FileTypeValidator({
                fileType: api_1.Consts.ALLOWED_IMAGE_FILE_TYPES.join("|"),
            }),
        ],
    }))),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object, Object]),
    __metadata("design:returntype", Promise)
], ReactorController.prototype, "updateCommunityImage", null);
__decorate([
    (0, common_1.Delete)("community/:id/image"),
    __param(0, (0, common_1.Param)("id", new zod_1.ZodPipe(api_1.Common.id))),
    __param(1, (0, current_user_decorator_1.HttpCurrentUser)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object]),
    __metadata("design:returntype", Promise)
], ReactorController.prototype, "deleteCommunityImage", null);
exports.ReactorController = ReactorController = __decorate([
    (0, common_1.Controller)("reactor"),
    (0, common_1.UseGuards)(session_auth_guard_1.HttpSessionAuthGuard),
    __metadata("design:paramtypes", [reactor_post_service_1.ReactorPostService,
        reactor_comment_service_1.ReactorCommentService,
        reactor_lens_service_1.ReactorLensService,
        reactor_hub_service_1.ReactorHubService,
        reactor_community_service_1.ReactorCommunityService])
], ReactorController);
//# sourceMappingURL=reactor.controller.js.map