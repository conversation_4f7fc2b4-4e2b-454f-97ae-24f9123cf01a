{"version": 3, "sources": ["../../src/acrpc/client.ts"], "sourcesContent": ["import type {\n    Method,\n    Schema,\n    SchemaEndpoint,\n    SchemaRoute,\n    Transformer,\n} from \"./core\";\n\nimport { z } from \"zod\";\nimport {\n    log,\n    dir,\n    kebabTransformer,\n    jsonTransformer,\n} from \"./core\";\n\ntype MaybePromise<T> = T | Promise<T>;\n\nfunction isEndpoint(schemaEntry: unknown): schemaEntry is SchemaEndpoint {\n    return (\n        schemaEntry != null && typeof schemaEntry === \"object\" && (\n            (\n                \"input\" in schemaEntry\n                && (\n                    schemaEntry[\"input\"] instanceof z.ZodType\n                    || schemaEntry[\"input\"] === null\n                    || schemaEntry[\"input\"] === undefined\n                )\n            )\n            && (\n                \"output\" in schemaEntry\n                && (\n                    schemaEntry[\"output\"] instanceof z.ZodType\n                    || schemaEntry[\"output\"] === null\n                    || schemaEntry[\"output\"] === undefined\n                )\n            )\n        )\n    );\n}\n\ntype ClientFetcherInit<TInterceptorContext = unknown> = RequestInit & {\n    fetch?: typeof fetch;\n    skipInterceptor?: boolean;\n    ctx?: TInterceptorContext;\n};\n\nexport type ClientRouteFetcher<TSchemaRoute extends SchemaRoute, TInterceptorContext> = {\n    [M in keyof TSchemaRoute]: TSchemaRoute[M] extends SchemaEndpoint\n        ? TSchemaRoute[M][\"input\"] extends null\n            ? (init?: ClientFetcherInit<TInterceptorContext>) => Promise<\n                TSchemaRoute[M][\"output\"] extends z.ZodType\n                    ? z.infer<TSchemaRoute[M][\"output\"]>\n                    : unknown\n            >\n            : TSchemaRoute[M][\"input\"] extends z.ZodType\n                ? (input: z.input<TSchemaRoute[M][\"input\"]>, init?: ClientFetcherInit<TInterceptorContext>) => Promise<\n                    TSchemaRoute[M][\"output\"] extends z.ZodType\n                        ? z.infer<TSchemaRoute[M][\"output\"]>\n                        : unknown\n                >\n                : (input?: unknown, init?: ClientFetcherInit<TInterceptorContext>) => Promise<\n                    TSchemaRoute[M][\"output\"] extends z.ZodType\n                        ? z.infer<TSchemaRoute[M][\"output\"]>\n                        : unknown\n                >\n        : TSchemaRoute[M] extends SchemaRoute\n            ? ClientRouteFetcher<TSchemaRoute[M], TInterceptorContext>\n            : never;\n};\n\nexport type ClientFetcher<TSchema extends Schema, TInterceptorContext> = {\n    [K in keyof TSchema]: TSchema[K] extends SchemaRoute\n        ? ClientRouteFetcher<TSchema[K], TInterceptorContext>\n        : TSchema[K] extends Schema\n            ? ClientFetcher<TSchema[K], TInterceptorContext>\n            : never;\n};\n\nexport type Client<TSchema extends Schema, TInterceptorContext = unknown> = ReturnType<typeof createClient<TSchema, TInterceptorContext>>;\n\nexport class HttpError extends Error {\n    method: string;\n    url: string;\n    status: number;\n    description: string;\n\n    constructor(\n        method: string,\n        url: string,\n        status: number,\n        description: string,\n    ) {\n        super(`Fetch at ${method.toUpperCase()} ${url} failed, status ${status}, description: '${description}'`);\n\n        this.method = method;\n        this.url = url;\n        this.status = status;\n        this.description = description;\n    }\n}\n\nfunction getLocalStorage() {\n    if (typeof window !== \"undefined\") {\n        return window.localStorage ?? null;\n    }\n\n    if (typeof globalThis !== undefined) {\n        return globalThis.localStorage ?? null;\n    }\n\n    return null;\n}\n\nfunction initCacheVersionMapEntryFactory(map: Map<string, number>) {\n    const now = Date.now();\n\n    return function initCacheVersionMapEntry(path: string) {\n        const localStorageKey = `acrpc:cache:${path}`;\n        const version = getLocalStorage()?.getItem(localStorageKey);\n    \n        // dir(\n        //     \"initializing cache version map entry\",\n        //     {\n        //         map,\n        //         path,\n        //         localStorageKey,\n        //         version\n        //     }\n        // );\n    \n        if (version) {\n            map.set(path, parseInt(version));\n        }\n        else {\n            getLocalStorage()?.setItem(localStorageKey, now.toString());\n            map.set(path, now);\n        }\n    \n        // dir(\"after\", { map });\n    }\n}\n\nfunction updateLocalStorageCacheVersionFactory(map: Map<string, number>) {\n    return function updateLocalStorageCacheVersion(path: string) {\n        const localStorageKey = `acrpc:cache:${path}`;\n    \n        const now = Date.now();\n    \n        getLocalStorage()?.setItem(localStorageKey, now.toString());\n        map.set(path, now);\n    }\n}\n\nfunction parsePathToMasterPaths(path: string): string[] {\n    if (!path.length) {\n        return [];\n    }\n\n    const parts = path.split(\"/\").slice(1);\n    const masterPaths: string[] = [];\n\n    for (let i = 0; i < parts.length; i++) {\n        masterPaths.push(\"/\" + parts.slice(0, i + 1).join(\"/\"));\n    }\n\n    return masterPaths;\n}\n\nfunction initMasterPathMapEntryFactory(map: Map<string, string[]>) {\n    return function initMasterPathMapEntry(path: string) {\n        const masterPaths = parsePathToMasterPaths(path);\n\n        // dir(\n        //     \"initializing master path map entry\",\n        //     {\n        //         path,\n        //         masterPaths,\n        //     }\n        // );\n\n        for (const masterPath of masterPaths) {\n            if (!map.has(masterPath)) {\n                map.set(masterPath, []);\n            }\n\n            map.get(masterPath)!.push(path);\n        }\n    }\n}\n\nfunction normalizeMasterPathMap(masterPathMap: Map<string, string[]>) {\n    for (const [masterPath, paths] of masterPathMap) {\n        masterPathMap.set(masterPath, [...new Set(paths)]);\n    }\n}\n\nfunction fillReverseMasterPathMap(\n    masterPathMap: Map<string, string[]>,\n    reverseMasterPathMap: Map<string, string[]>,\n) {\n    for (const [masterPath, paths] of masterPathMap) {\n        for (const path of paths) {\n            if (!reverseMasterPathMap.has(path)) {\n                reverseMasterPathMap.set(path, []);\n            }\n\n            reverseMasterPathMap.get(path)!.push(masterPath);\n        }\n    }\n}\n\nfunction hydrateInvalidPathCacheSet(invalidPathCacheSet: Set<string>) {\n    const invalidPaths = getLocalStorage()?.getItem(\"acrpc:invalid-paths\");\n\n    if (invalidPaths) {\n        try {\n            const parsedInvalidPaths = JSON.parse(invalidPaths);\n\n            for (const invalidPath of parsedInvalidPaths) {\n                invalidPathCacheSet.add(invalidPath);\n            }\n        }\n        catch (error) {\n            console.error(\"Error parsing invalid paths\", error);\n\n            getLocalStorage()?.removeItem(\"acrpc:invalid-paths\");\n        }\n    }\n}\n\nfunction invalidatePathCache2Factory(\n    masterPathMap: Map<string, string[]>,\n    reverseMasterPathMap: Map<string, string[]>,\n    invalidPathCacheSet: Set<string>,\n) {\n    return function invalidatePathCache2(path: string, depth: number) {\n        const masterPaths = masterPathMap.get(path) ?? [];\n\n        dir(\n            \"invalidating path cache\",\n            {\n                path,\n                depth,\n                masterPaths,\n            },\n        );\n\n        const masterPath = masterPaths[Math.max(0, masterPaths.length - depth)];\n        const paths = reverseMasterPathMap.get(masterPath) ?? [];\n\n        dir(\n            \"invalidating path cache 2\",\n            {\n                masterPath,\n                paths,\n            },\n        );\n\n        for (const path of paths) {\n            invalidPathCacheSet.add(path);\n        }\n\n        getLocalStorage()?.setItem(\n            \"acrpc:invalid-paths\",\n            JSON.stringify([...invalidPathCacheSet]),\n        );\n    }\n}\n\nexport function createClient<TSchema extends Schema, TInterceptorContext = unknown>(\n    schema: TSchema,\n    options: {\n        entrypointUrl: string;\n        transformer?: Transformer;\n        init?: RequestInit;\n        fetch?: typeof fetch;\n        interceptor?: (data: {\n            method: Method;\n            path: string;\n            response: Response;\n            ctx?: TInterceptorContext;\n        }) => MaybePromise<void>;\n    },\n) {\n    const transformer = options.transformer ?? jsonTransformer;\n    const url = options.entrypointUrl;\n\n    const masterPathMap = new Map<string, string[]>();\n    const reverseMasterPathMap = new Map<string, string[]>();\n    const invalidPathCacheSet = new Set<string>();\n\n    const initMasterPathMapEntry = initMasterPathMapEntryFactory(masterPathMap);\n\n    const invalidatePathCache2 = invalidatePathCache2Factory(\n        masterPathMap,\n        reverseMasterPathMap,\n        invalidPathCacheSet,\n    );\n\n    dir({\n        invalidPathCacheSet,\n    });\n\n    const baseFetch = options.fetch ?? fetch;\n    const baseInit: RequestInit = { ...options.init };\n\n    function fillClientFetcher(\n        schema: Schema,\n        names: readonly string[],\n        result: any,\n    ) {\n        // dir({ url, names });\n    \n        for (const [name, schemaEntry] of Object.entries(schema) as [string, any][]) {\n            const kebabName = kebabTransformer.transform(name);\n    \n            if (isEndpoint(schemaEntry)) {\n                const path = [\"\", ...names].join(\"/\");\n                const method = name as Method;\n    \n                // initCacheVersionMapEntry(path);\n                initMasterPathMapEntry(path);\n\n                function parseArgs(args: any[]): [unknown, ClientFetcherInit<TInterceptorContext> | undefined] {\n                    if (schemaEntry.input === null) {\n                        return [undefined, { ...args[0] }];\n                    }\n\n                    return [args[0], args[1]];\n                }\n    \n                const obj = {\n                    [method]: async function (...args: any[]) {\n                        const [input, init] = parseArgs(args);\n\n                        if (schemaEntry.input != null && !input) {\n                            throw new Error(\"Input data argument not provided.\")\n                        }\n\n                        log(`Performing ${method.toUpperCase()} ${path}...`);\n                        // dir({ input, init });\n    \n                        const currentUrl = new URL(path, url);\n\n                        const isInvalidCache = invalidPathCacheSet.has(path);\n                        // const cacheVersion = cacheVersionMap.get(path)!;\n                        // dir({ initialUrl: currentUrl, cacheVersion });\n    \n                        // currentUrl.searchParams.set(\"v\", cacheVersion.toString());\n\n                        const requestInit = {\n                            ...baseInit,\n                            ...init,\n\n                            headers: {\n                                ...baseInit.headers,\n                                ...init?.headers,\n\n                                ...(\n                                    isInvalidCache\n                                    ? {\n                                        \"Cache-Control\": \"reload\",\n                                    }\n                                    : null\n                                )\n                            } as Record<string, string>,\n\n                            method: method.toUpperCase(),\n                        }\n    \n                        if (schemaEntry.input !== null && input !== undefined) {\n                            const serializedInput = transformer.serialize(input);\n                            // dir({ serializedInput });\n\n                            if (method === \"get\") {\n                                currentUrl.searchParams.set(\n                                    \"__body\",\n                                    encodeURIComponent(serializedInput),\n                                );\n                            }\n                            else {\n                                requestInit.headers[\"Content-Type\"] = \"application/json\";\n                                requestInit.body = serializedInput;\n                            }\n                        }\n    \n                        // dir({ finalUrl: currentUrl });\n\n                        const fetch = init?.fetch ?? baseFetch;\n                        delete init?.fetch;\n    \n                        const fetchResult = await fetch(\n                            currentUrl.origin + currentUrl.pathname + currentUrl.search,\n                            requestInit,\n                        );\n\n                        if (!init?.skipInterceptor) {\n                            await options.interceptor?.({\n                                method,\n                                path,\n                                response: fetchResult,\n                                ctx: init?.ctx,\n                            });\n                        }\n    \n                        if (fetchResult.ok) {\n                            let output: unknown = null;\n\n                            if (schemaEntry.output !== null) {\n                                const rawOutput = await fetchResult.text();\n                                output = transformer.deserialize(rawOutput);\n                            }\n    \n                            // dir({\n                            //     rawOutput,\n                            //     preparedOutput,\n                            // });\n\n                            dir({\n                                autoScopeInvalidationDepth: schemaEntry.autoScopeInvalidationDepth,\n                                invalidate: schemaEntry.invalidate,\n                            });\n\n                            dir(\n                                \"before invalidations\",\n                                {\n                                    masterPathMap,\n                                    // cacheVersionMap,\n                                    invalidPathCacheSet,\n                                }\n                            );\n\n                            const autoScopeInvalidationDepth = schemaEntry.autoScopeInvalidationDepth ?? 0;\n    \n                            if (autoScopeInvalidationDepth) {\n                                invalidatePathCache2(path, autoScopeInvalidationDepth);\n                            }\n    \n                            if (schemaEntry.invalidate) {\n                                for (const invalidate of schemaEntry.invalidate) {\n                                    invalidatePathCache2(invalidate, 0);\n                                }\n                            }\n\n                            dir(\n                                \"after invalidations\",\n                                {\n                                    masterPathMap,\n                                    // cacheVersionMap,\n                                    invalidPathCacheSet,\n                                }\n                            )\n    \n                            return output;\n                        }\n                        \n                        throw new HttpError(\n                            method,\n                            path,\n                            fetchResult.status,\n                            await fetchResult.text() || fetchResult.statusText,\n                        )\n                    },\n                };\n    \n                Object.assign(result, obj);\n            }\n            else {\n                const nestedResult = result[name] = {};\n    \n                fillClientFetcher(\n                    schemaEntry,\n                    [...names, kebabName],\n                    nestedResult,\n                );\n            }\n        }\n    \n        return result;\n    }\n\n    const fetcher: ClientFetcher<TSchema, TInterceptorContext> = fillClientFetcher(\n        schema,\n        [],\n        {},\n    );\n\n    normalizeMasterPathMap(masterPathMap);\n    fillReverseMasterPathMap(masterPathMap, reverseMasterPathMap);\n\n    hydrateInvalidPathCacheSet(invalidPathCacheSet);\n\n    return {\n        fetcher,\n    };\n}\n"], "mappings": ";;;;;;;;;AAQA,SAAS,SAAS;AAUlB,SAAS,WAAW,aAAqD;AACrE,SACI,eAAe,QAAQ,OAAO,gBAAgB,aAEtC,WAAW,gBAEP,YAAY,OAAO,aAAa,EAAE,WAC/B,YAAY,OAAO,MAAM,QACzB,YAAY,OAAO,MAAM,YAIhC,YAAY,gBAER,YAAY,QAAQ,aAAa,EAAE,WAChC,YAAY,QAAQ,MAAM,QAC1B,YAAY,QAAQ,MAAM;AAKjD;AA0CO,IAAM,YAAN,cAAwB,MAAM;AAAA,EACjC;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EAEA,YACI,QACA,KACA,QACA,aACF;AACE,UAAM,YAAY,OAAO,YAAY,CAAC,IAAI,GAAG,mBAAmB,MAAM,mBAAmB,WAAW,GAAG;AAEvG,SAAK,SAAS;AACd,SAAK,MAAM;AACX,SAAK,SAAS;AACd,SAAK,cAAc;AAAA,EACvB;AACJ;AAEA,SAAS,kBAAkB;AACvB,MAAI,OAAO,WAAW,aAAa;AAC/B,WAAO,OAAO,gBAAgB;AAAA,EAClC;AAEA,MAAI,OAAO,eAAe,QAAW;AACjC,WAAO,WAAW,gBAAgB;AAAA,EACtC;AAEA,SAAO;AACX;AA0CA,SAAS,uBAAuB,MAAwB;AACpD,MAAI,CAAC,KAAK,QAAQ;AACd,WAAO,CAAC;AAAA,EACZ;AAEA,QAAM,QAAQ,KAAK,MAAM,GAAG,EAAE,MAAM,CAAC;AACrC,QAAM,cAAwB,CAAC;AAE/B,WAAS,IAAI,GAAG,IAAI,MAAM,QAAQ,KAAK;AACnC,gBAAY,KAAK,MAAM,MAAM,MAAM,GAAG,IAAI,CAAC,EAAE,KAAK,GAAG,CAAC;AAAA,EAC1D;AAEA,SAAO;AACX;AAEA,SAAS,8BAA8B,KAA4B;AAC/D,SAAO,SAAS,uBAAuB,MAAc;AACjD,UAAM,cAAc,uBAAuB,IAAI;AAU/C,eAAW,cAAc,aAAa;AAClC,UAAI,CAAC,IAAI,IAAI,UAAU,GAAG;AACtB,YAAI,IAAI,YAAY,CAAC,CAAC;AAAA,MAC1B;AAEA,UAAI,IAAI,UAAU,EAAG,KAAK,IAAI;AAAA,IAClC;AAAA,EACJ;AACJ;AAEA,SAAS,uBAAuB,eAAsC;AAClE,aAAW,CAAC,YAAY,KAAK,KAAK,eAAe;AAC7C,kBAAc,IAAI,YAAY,CAAC,GAAG,IAAI,IAAI,KAAK,CAAC,CAAC;AAAA,EACrD;AACJ;AAEA,SAAS,yBACL,eACA,sBACF;AACE,aAAW,CAAC,YAAY,KAAK,KAAK,eAAe;AAC7C,eAAW,QAAQ,OAAO;AACtB,UAAI,CAAC,qBAAqB,IAAI,IAAI,GAAG;AACjC,6BAAqB,IAAI,MAAM,CAAC,CAAC;AAAA,MACrC;AAEA,2BAAqB,IAAI,IAAI,EAAG,KAAK,UAAU;AAAA,IACnD;AAAA,EACJ;AACJ;AAEA,SAAS,2BAA2B,qBAAkC;AAClE,QAAM,eAAe,gBAAgB,GAAG,QAAQ,qBAAqB;AAErE,MAAI,cAAc;AACd,QAAI;AACA,YAAM,qBAAqB,KAAK,MAAM,YAAY;AAElD,iBAAW,eAAe,oBAAoB;AAC1C,4BAAoB,IAAI,WAAW;AAAA,MACvC;AAAA,IACJ,SACO,OAAO;AACV,cAAQ,MAAM,+BAA+B,KAAK;AAElD,sBAAgB,GAAG,WAAW,qBAAqB;AAAA,IACvD;AAAA,EACJ;AACJ;AAEA,SAAS,4BACL,eACA,sBACA,qBACF;AACE,SAAO,SAAS,qBAAqB,MAAc,OAAe;AAC9D,UAAM,cAAc,cAAc,IAAI,IAAI,KAAK,CAAC;AAEhD;AAAA,MACI;AAAA,MACA;AAAA,QACI;AAAA,QACA;AAAA,QACA;AAAA,MACJ;AAAA,IACJ;AAEA,UAAM,aAAa,YAAY,KAAK,IAAI,GAAG,YAAY,SAAS,KAAK,CAAC;AACtE,UAAM,QAAQ,qBAAqB,IAAI,UAAU,KAAK,CAAC;AAEvD;AAAA,MACI;AAAA,MACA;AAAA,QACI;AAAA,QACA;AAAA,MACJ;AAAA,IACJ;AAEA,eAAWA,SAAQ,OAAO;AACtB,0BAAoB,IAAIA,KAAI;AAAA,IAChC;AAEA,oBAAgB,GAAG;AAAA,MACf;AAAA,MACA,KAAK,UAAU,CAAC,GAAG,mBAAmB,CAAC;AAAA,IAC3C;AAAA,EACJ;AACJ;AAEO,SAAS,aACZ,QACA,SAYF;AACE,QAAM,cAAc,QAAQ,eAAe;AAC3C,QAAM,MAAM,QAAQ;AAEpB,QAAM,gBAAgB,oBAAI,IAAsB;AAChD,QAAM,uBAAuB,oBAAI,IAAsB;AACvD,QAAM,sBAAsB,oBAAI,IAAY;AAE5C,QAAM,yBAAyB,8BAA8B,aAAa;AAE1E,QAAM,uBAAuB;AAAA,IACzB;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AAEA,MAAI;AAAA,IACA;AAAA,EACJ,CAAC;AAED,QAAM,YAAY,QAAQ,SAAS;AACnC,QAAM,WAAwB,EAAE,GAAG,QAAQ,KAAK;AAEhD,WAAS,kBACLC,SACA,OACA,QACF;AAGE,eAAW,CAAC,MAAM,WAAW,KAAK,OAAO,QAAQA,OAAM,GAAsB;AACzE,YAAM,YAAY,iBAAiB,UAAU,IAAI;AAEjD,UAAI,WAAW,WAAW,GAAG;AAOzB,YAAS,YAAT,SAAmB,MAA4E;AAC3F,cAAI,YAAY,UAAU,MAAM;AAC5B,mBAAO,CAAC,QAAW,EAAE,GAAG,KAAK,CAAC,EAAE,CAAC;AAAA,UACrC;AAEA,iBAAO,CAAC,KAAK,CAAC,GAAG,KAAK,CAAC,CAAC;AAAA,QAC5B;AAZA,cAAM,OAAO,CAAC,IAAI,GAAG,KAAK,EAAE,KAAK,GAAG;AACpC,cAAM,SAAS;AAGf,+BAAuB,IAAI;AAU3B,cAAM,MAAM;AAAA,UACR,CAAC,MAAM,GAAG,kBAAmB,MAAa;AACtC,kBAAM,CAAC,OAAO,IAAI,IAAI,UAAU,IAAI;AAEpC,gBAAI,YAAY,SAAS,QAAQ,CAAC,OAAO;AACrC,oBAAM,IAAI,MAAM,mCAAmC;AAAA,YACvD;AAEA,gBAAI,cAAc,OAAO,YAAY,CAAC,IAAI,IAAI,KAAK;AAGnD,kBAAM,aAAa,IAAI,IAAI,MAAM,GAAG;AAEpC,kBAAM,iBAAiB,oBAAoB,IAAI,IAAI;AAMnD,kBAAM,cAAc;AAAA,cAChB,GAAG;AAAA,cACH,GAAG;AAAA,cAEH,SAAS;AAAA,gBACL,GAAG,SAAS;AAAA,gBACZ,GAAG,MAAM;AAAA,gBAET,GACI,iBACE;AAAA,kBACE,iBAAiB;AAAA,gBACrB,IACE;AAAA,cAEV;AAAA,cAEA,QAAQ,OAAO,YAAY;AAAA,YAC/B;AAEA,gBAAI,YAAY,UAAU,QAAQ,UAAU,QAAW;AACnD,oBAAM,kBAAkB,YAAY,UAAU,KAAK;AAGnD,kBAAI,WAAW,OAAO;AAClB,2BAAW,aAAa;AAAA,kBACpB;AAAA,kBACA,mBAAmB,eAAe;AAAA,gBACtC;AAAA,cACJ,OACK;AACD,4BAAY,QAAQ,cAAc,IAAI;AACtC,4BAAY,OAAO;AAAA,cACvB;AAAA,YACJ;AAIA,kBAAMC,SAAQ,MAAM,SAAS;AAC7B,mBAAO,MAAM;AAEb,kBAAM,cAAc,MAAMA;AAAA,cACtB,WAAW,SAAS,WAAW,WAAW,WAAW;AAAA,cACrD;AAAA,YACJ;AAEA,gBAAI,CAAC,MAAM,iBAAiB;AACxB,oBAAM,QAAQ,cAAc;AAAA,gBACxB;AAAA,gBACA;AAAA,gBACA,UAAU;AAAA,gBACV,KAAK,MAAM;AAAA,cACf,CAAC;AAAA,YACL;AAEA,gBAAI,YAAY,IAAI;AAChB,kBAAI,SAAkB;AAEtB,kBAAI,YAAY,WAAW,MAAM;AAC7B,sBAAM,YAAY,MAAM,YAAY,KAAK;AACzC,yBAAS,YAAY,YAAY,SAAS;AAAA,cAC9C;AAOA,kBAAI;AAAA,gBACA,4BAA4B,YAAY;AAAA,gBACxC,YAAY,YAAY;AAAA,cAC5B,CAAC;AAED;AAAA,gBACI;AAAA,gBACA;AAAA,kBACI;AAAA;AAAA,kBAEA;AAAA,gBACJ;AAAA,cACJ;AAEA,oBAAM,6BAA6B,YAAY,8BAA8B;AAE7E,kBAAI,4BAA4B;AAC5B,qCAAqB,MAAM,0BAA0B;AAAA,cACzD;AAEA,kBAAI,YAAY,YAAY;AACxB,2BAAW,cAAc,YAAY,YAAY;AAC7C,uCAAqB,YAAY,CAAC;AAAA,gBACtC;AAAA,cACJ;AAEA;AAAA,gBACI;AAAA,gBACA;AAAA,kBACI;AAAA;AAAA,kBAEA;AAAA,gBACJ;AAAA,cACJ;AAEA,qBAAO;AAAA,YACX;AAEA,kBAAM,IAAI;AAAA,cACN;AAAA,cACA;AAAA,cACA,YAAY;AAAA,cACZ,MAAM,YAAY,KAAK,KAAK,YAAY;AAAA,YAC5C;AAAA,UACJ;AAAA,QACJ;AAEA,eAAO,OAAO,QAAQ,GAAG;AAAA,MAC7B,OACK;AACD,cAAM,eAAe,OAAO,IAAI,IAAI,CAAC;AAErC;AAAA,UACI;AAAA,UACA,CAAC,GAAG,OAAO,SAAS;AAAA,UACpB;AAAA,QACJ;AAAA,MACJ;AAAA,IACJ;AAEA,WAAO;AAAA,EACX;AAEA,QAAM,UAAuD;AAAA,IACzD;AAAA,IACA,CAAC;AAAA,IACD,CAAC;AAAA,EACL;AAEA,yBAAuB,aAAa;AACpC,2BAAyB,eAAe,oBAAoB;AAE5D,6BAA2B,mBAAmB;AAE9C,SAAO;AAAA,IACH;AAAA,EACJ;AACJ;", "names": ["path", "schema", "fetch"]}