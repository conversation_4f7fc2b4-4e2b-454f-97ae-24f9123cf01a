import { Prisma } from "@prisma/client";
import {
    BadRequestException,
    ForbiddenException,
    Injectable,
    Logger,
} from "@nestjs/common";
import { getError } from "src/common/errors";
import { CurrentUser } from "src/auth/types";
import { PrismaService } from "src/prisma/prisma.service";
import { tokenize, createAst, validate, generateSql } from "./functions";
import { Common, Reactor } from "@commune/api";

@Injectable()
export class ReactorLensService {
    private readonly logger = new Logger(ReactorLensService.name);

    constructor(private readonly prisma: PrismaService) {}

    private generateSql(code: string) {
        const { tokens } = tokenize(code);
        const ast = createAst(tokens);
        const statement = validate(ast);
        const sql = generateSql(statement);

        return sql;
    }

    async getLenses(user: CurrentUser) {
        return this.prisma.reactorLens.findMany({
            where: {
                userId: user.id,
                deletedAt: null,
            },
        });
    }

    private generateLensSql(code: string) {
        try {
            return this.generateSql(code);
        } catch (error) {
            this.logger.error(error);

            throw new BadRequestException(String(error));
        }
    }

    async createLens(dto: { name: string; code: string }, user: CurrentUser) {
        const sql = this.generateLensSql(dto.code);

        const lens = await this.prisma.reactorLens.create({
            data: {
                userId: user.id,
                name: dto.name,
                code: dto.code,
                sql,
            },
        });

        return { id: lens.id };
    }

    async updateLens(input: Reactor.UpdateLensInput, user: CurrentUser) {
        const lens = await this.prisma.reactorLens.findUniqueOrThrow({
            where: { id: input.id, deletedAt: null },
        });

        if (user.role !== "admin" && lens.userId !== user.id) {
            throw new ForbiddenException(
                ...getError("must_be_admin_or_author"),
            );
        }

        const data: Prisma.ReactorLensUpdateInput = Object.assign(
            {},
            input.name ? { name: input.name } : undefined,
            input.code
                ? { code: input.code, sql: this.generateLensSql(input.code) }
                : null,
        );

        await this.prisma.reactorLens.update({
            where: { id: input.id },
            data,
        });

        return true;
    }

    async deleteLens(input: Common.ObjectWithId, user: CurrentUser) {
        const lens = await this.prisma.reactorLens.findUniqueOrThrow({
            where: { id: input.id, deletedAt: null },
        });

        if (user.role !== "admin" && lens.userId !== user.id) {
            throw new ForbiddenException(
                ...getError("must_be_admin_or_author"),
            );
        }

        await this.prisma.reactorLens.update({
            where: { id: input.id },
            data: {
                deletedAt: new Date(),
            },
        });
    }
}
