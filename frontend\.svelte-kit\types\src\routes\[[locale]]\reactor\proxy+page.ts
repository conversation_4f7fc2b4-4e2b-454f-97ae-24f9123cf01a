// @ts-nocheck
import type { PageLoad } from "./$types";

import { Consts } from "@commune/api";
import { getClient } from "$lib/acrpc";

export const load = async ({ fetch, url }: Parameters<PageLoad>[0]) => {
  const { fetcher: api } = getClient();

  const posts = await api.reactor.post.list.get({ lensId: null }, { fetch, ctx: { url } });

  return {
    posts,
    isHasMorePosts: posts.length === Consts.PAGE_SIZE,
  };
};
