<script lang="ts">
  import type { Common } from "@commune/api";

  import { preventDefault } from "$lib";
  import { getClient } from "$lib/acrpc";
  import { Modal } from "$components";
  import { LocalizedInput, LocalizedTextarea } from "../../components";

  interface Props {
    show: boolean;
    onHide: () => void;
    locale: Common.LocalizationLocale;
    communeData: {
      id: string;
      name: Common.Localizations;
      description: Common.Localizations;
    };
    onCommuneUpdated: () => void;
  }

  const i18n = {
    en: {
      editCommune: "Edit Commune",
      communeUpdatedSuccess: "Commune updated successfully!",
      name: "Name",
      enterCommuneName: "Enter commune name",
      description: "Description (optional)",
      enterCommuneDescription: "Enter commune description",
      cancel: "Cancel",
      save: "Save Changes",
      saving: "Saving...",
      provideName: "Please provide a name for the commune.",
      failedToUpdate: "Failed to update commune",
      unexpectedError: "An unexpected error occurred. Please try again.",
    },
    ru: {
      editCommune: "Редактировать коммуну",
      communeUpdatedSuccess: "Коммуна успешно обновлена!",
      name: "Название",
      enterCommuneName: "Введите название коммуны",
      description: "Описание (опционально)",
      enterCommuneDescription: "Введите описание коммуны",
      cancel: "Отмена",
      save: "Сохранить изменения",
      saving: "Сохранение...",
      provideName: "Пожалуйста, укажите название коммуны.",
      failedToUpdate: "Не удалось обновить коммуну",
      unexpectedError: "Произошла непредвиденная ошибка. Пожалуйста, попробуйте снова.",
    },
  };

  const { fetcher: api } = getClient();

  const { show, onHide, locale, communeData, onCommuneUpdated }: Props = $props();

  const t = $derived(i18n[locale]);

  let name = $state<Common.Localizations>([]);
  let description = $state<Common.Localizations>([]);
  let error = $state<string>("");
  let isSubmitting = $state(false);
  let submitSuccess = $state(false);

  // Initialize form with commune data when modal opens or communeData changes
  $effect(() => {
    if (communeData && show) {
      name = communeData.name || [];
      description = communeData.description || [];
    }
  });

  async function handleSubmit() {
    if (!name.some((item) => item.value.trim().length)) {
      error = t.provideName;
      return;
    }

    isSubmitting = true;
    error = "";

    try {
      await api.commune.patch({
        id: communeData?.id,
        name,
        description,
      });

      submitSuccess = true;
      onCommuneUpdated();

      setTimeout(() => {
        handleClose();
      }, 1500);
    } catch (err) {
      error = err instanceof Error ? err.message : t.unexpectedError;
      console.error(err);
    } finally {
      isSubmitting = false;
    }
  }

  function handleClose() {
    name = [];
    description = [];
    error = "";
    submitSuccess = false;
    onHide();
  }
</script>

<Modal
  {show}
  title={t.editCommune}
  onClose={handleClose}
  onSubmit={handleSubmit}
  submitText={isSubmitting ? t.saving : t.save}
  cancelText={t.cancel}
  submitDisabled={!name.some((item) => item.value.trim().length) || isSubmitting}
  cancelDisabled={isSubmitting}
  {isSubmitting}
>
  {#if submitSuccess}
    <div class="alert alert-success mb-3">
      {t.communeUpdatedSuccess}
    </div>
  {/if}

  {#if error}
    <div class="alert alert-danger mb-3">
      {error}
    </div>
  {/if}

  <form onsubmit={preventDefault(handleSubmit)}>
    <LocalizedInput
      id="communeName"
      label={t.name}
      placeholder={t.enterCommuneName}
      required={true}
      {locale}
      bind:value={name}
    />

    <LocalizedTextarea
      id="communeDescription"
      label={t.description}
      placeholder={t.enterCommuneDescription}
      rows={4}
      {locale}
      bind:value={description}
    />
  </form>
</Modal>
