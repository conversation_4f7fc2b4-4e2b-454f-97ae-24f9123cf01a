import { Reactor } from "@commune/api";
import { CurrentUser } from "src/auth/types";
import { MinioService } from "src/minio/minio.service";
import { PrismaService } from "src/prisma/prisma.service";
export declare class ReactorHubService {
    private readonly prisma;
    private readonly minioService;
    private readonly logger;
    constructor(prisma: PrismaService, minioService: MinioService);
    getHubs(input: Reactor.GetHubsInput, user: CurrentUser): Promise<{
        id: string;
        createdAt: Date;
        updatedAt: Date;
        deletedAt: Date | null;
        headUser: {
            image: {
                id: string;
                createdAt: Date;
                updatedAt: Date;
                deletedAt: Date | null;
                url: string;
            } | null;
            name: {
                id: string;
                createdAt: Date;
                updatedAt: Date;
                deletedAt: Date | null;
                key: string;
                locale: import("@prisma/client").$Enums.Locale;
                value: string;
            }[];
        } & {
            id: string;
            imageId: string | null;
            createdAt: Date;
            updatedAt: Date;
            deletedAt: Date | null;
            referrerId: string | null;
            email: string;
            role: import("@prisma/client").$Enums.UserRole;
        };
        image: {
            id: string;
            createdAt: Date;
            updatedAt: Date;
            deletedAt: Date | null;
            url: string;
        } | null;
        name: {
            id: string;
            createdAt: Date;
            updatedAt: Date;
            deletedAt: Date | null;
            key: string;
            locale: import("@prisma/client").$Enums.Locale;
            value: string;
        }[];
        description: {
            id: string;
            createdAt: Date;
            updatedAt: Date;
            deletedAt: Date | null;
            key: string;
            locale: import("@prisma/client").$Enums.Locale;
            value: string;
        }[];
    }[]>;
    createHub(input: Reactor.CreateHubInput, user: CurrentUser): Promise<{
        id: string;
        headUserId: string;
        imageId: string | null;
        createdAt: Date;
        updatedAt: Date;
        deletedAt: Date | null;
    }>;
    updateHub(input: Reactor.UpdateHubInput, user: CurrentUser): Promise<void>;
    updateHubImage(id: string, file: Express.Multer.File, user: CurrentUser): Promise<void>;
    deleteHubImage(id: string, user: CurrentUser): Promise<void>;
}
