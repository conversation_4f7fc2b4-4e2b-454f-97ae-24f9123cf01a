import * as client_hooks from '../../../src/hooks.client.ts';


export { matchers } from './matchers.js';

export const nodes = [
	() => import('./nodes/0'),
	() => import('./nodes/1'),
	() => import('./nodes/2'),
	() => import('./nodes/3'),
	() => import('./nodes/4'),
	() => import('./nodes/5'),
	() => import('./nodes/6'),
	() => import('./nodes/7'),
	() => import('./nodes/8'),
	() => import('./nodes/9'),
	() => import('./nodes/10'),
	() => import('./nodes/11'),
	() => import('./nodes/12'),
	() => import('./nodes/13'),
	() => import('./nodes/14'),
	() => import('./nodes/15'),
	() => import('./nodes/16'),
	() => import('./nodes/17'),
	() => import('./nodes/18'),
	() => import('./nodes/19'),
	() => import('./nodes/20'),
	() => import('./nodes/21'),
	() => import('./nodes/22'),
	() => import('./nodes/23'),
	() => import('./nodes/24'),
	() => import('./nodes/25'),
	() => import('./nodes/26'),
	() => import('./nodes/27'),
	() => import('./nodes/28')
];

export const server_loads = [2];

export const dictionary = {
		"/[[locale]]/auth": [21,[2]],
		"/[[locale]]/(index)/communes": [6,[2,3]],
		"/[[locale]]/(index)/communes/invitations": [7,[2,3]],
		"/[[locale]]/(index)/communes/join-requests": [8,[2,3]],
		"/[[locale]]/(index)/communes/[id]": [9,[2,3]],
		"/[[locale]]/(index)/communes/[id]/invitations": [10,[2,3]],
		"/[[locale]]/(index)/communes/[id]/join-requests": [11,[2,3]],
		"/[[locale]]/(index)/new-calendar": [12,[2,3]],
		"/[[locale]]/(index)/new-english": [13,[2,3]],
		"/[[locale]]/(index)/profile": [14,[2,3]],
		"/[[locale]]/reactor": [22,[2,4]],
		"/[[locale]]/reactor/communities": [23,[2,4]],
		"/[[locale]]/reactor/communities/[id]": [24,[2,4]],
		"/[[locale]]/reactor/hubs": [25,[2,4]],
		"/[[locale]]/reactor/hubs/[id]": [26,[2,4]],
		"/[[locale]]/reactor/[id]": [27,[2,4]],
		"/[[locale]]/(index)/rules": [15,[2,3]],
		"/[[locale]]/test/tag": [28,[2]],
		"/[[locale]]/(index)/the-law": [16,[2,3]],
		"/[[locale]]/(index)/users": [17,[2,3]],
		"/[[locale]]/(index)/users/[id]": [18,[2,3]],
		"/[[locale]]/(index)/users/[id]/feedback": [19,[2,3]],
		"/[[locale]]/(index)/users/[id]/karma": [20,[2,3]],
		"/[[locale]]/(index)": [5,[2,3]]
	};

export const hooks = {
	handleError: client_hooks.handleError || (({ error }) => { console.error(error) }),
	init: client_hooks.init,
	reroute: (() => {}),
	transport: {}
};

export const decoders = Object.fromEntries(Object.entries(hooks.transport).map(([k, v]) => [k, v.decode]));

export const hash = false;

export const decode = (type, value) => decoders[type](value);

export { default as root } from '../root.js';