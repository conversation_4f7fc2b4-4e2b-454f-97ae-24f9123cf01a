"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var MinioService_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.MinioService = exports.minioImageEntities = void 0;
const zod_1 = require("zod");
const minio_1 = require("minio");
const config_1 = require("@nestjs/config");
const common_1 = require("@nestjs/common");
exports.minioImageEntities = [
    "commune",
    "user",
    "post",
    "reactor-hub",
    "reactor-community",
];
const minioSchema = {
    endpoint: zod_1.z.string().nonempty(),
    port: zod_1.z.coerce.number().int().positive(),
    accessKey: zod_1.z.string().nonempty(),
    secretKey: zod_1.z.string().nonempty(),
    useSSL: zod_1.z.coerce.boolean().default(false),
};
let MinioService = MinioService_1 = class MinioService {
    constructor(configService) {
        this.configService = configService;
        this.logger = new common_1.Logger(MinioService_1.name);
        this.buckets = exports.minioImageEntities.reduce((acc, entity) => {
            acc[entity] = entity;
            return acc;
        }, {});
        const endPoint = minioSchema.endpoint.parse(this.configService.get("MINIO_ENDPOINT"));
        const port = minioSchema.port.parse(this.configService.get("MINIO_PORT"));
        const accessKey = minioSchema.accessKey.parse(this.configService.get("MINIO_ACCESS_KEY"));
        const secretKey = minioSchema.secretKey.parse(this.configService.get("MINIO_SECRET_KEY"));
        const useSSL = minioSchema.useSSL.parse(this.configService.get("MINIO_USE_SSL"));
        this.client = new minio_1.Client({
            endPoint,
            port,
            useSSL,
            accessKey,
            secretKey,
        });
        this.initializeBuckets();
    }
    async initializeBuckets() {
        await Promise.all(Object.values(this.buckets).map(async (bucket) => {
            const exists = await this.client.bucketExists(bucket);
            if (!exists) {
                await this.client.makeBucket(bucket, "us-east-1");
                this.logger.log(`Bucket '${bucket}' created successfully`);
                const policy = {
                    Version: "2012-10-17",
                    Statement: [
                        {
                            Effect: "Allow",
                            Principal: { AWS: ["*"] },
                            Action: ["s3:GetObject"],
                            Resource: [`arn:aws:s3:::${bucket}/*`],
                        },
                    ],
                };
                await this.client.setBucketPolicy(bucket, JSON.stringify(policy));
                this.logger.log(`Public read policy set for bucket '${bucket}'`);
            }
        })).catch((error) => {
            this.logger.error("Failed to initialize MinIO buckets", error);
        });
    }
    getImageObjectName(entityId, index) {
        return `${entityId}${index !== undefined ? `.${index}` : ""}`;
    }
    async uploadImage(file, entity, entityId, index) {
        const bucket = this.buckets[entity];
        const objectName = this.getImageObjectName(entityId, index);
        if (!bucket) {
            throw new Error(`There's no bucket for entity: ${entity}`);
        }
        return await this.uploadFile(file, bucket, objectName);
    }
    async uploadFile(file, bucket, objectName) {
        try {
            await this.client.putObject(bucket, objectName, file.buffer, undefined, {
                "Content-Type": file.mimetype,
            });
            return `${bucket}/${objectName}`;
        }
        catch (error) {
            this.logger.error(`Failed to upload file ${objectName} to bucket ${bucket}`, error);
            throw error;
        }
    }
    async deleteImage(entity, entityId, index) {
        const bucket = this.buckets[entity];
        const objectName = this.getImageObjectName(entityId, index);
        return await this.deleteFile(bucket, objectName);
    }
    async deleteFile(bucket, objectName) {
        try {
            await this.client.removeObject(bucket, objectName);
        }
        catch (error) {
            throw new Error(`Failed to delete file ${objectName} from bucket ${bucket}`, { cause: error });
        }
    }
};
exports.MinioService = MinioService;
exports.MinioService = MinioService = MinioService_1 = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [config_1.ConfigService])
], MinioService);
//# sourceMappingURL=minio.service.js.map