"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var ReactorCommentService_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.ReactorCommentService = void 0;
const common_1 = require("@nestjs/common");
const errors_1 = require("../common/errors");
const prisma_service_1 = require("../prisma/prisma.service");
const rating_service_1 = require("../rating/rating.service");
const library_1 = require("@prisma/client/runtime/library");
let ReactorCommentService = ReactorCommentService_1 = class ReactorCommentService {
    constructor(prisma, ratingService) {
        this.prisma = prisma;
        this.ratingService = ratingService;
        this.logger = new common_1.Logger(ReactorCommentService_1.name);
    }
    async getComments(input, user) {
        let commentId = null;
        let masterPostId = null;
        if (input.id === undefined) {
            if (input.entityType === "comment") {
                throw new common_1.BadRequestException(...(0, errors_1.getError)("get_comments_for_comment_not_implemented"));
            }
            await this.prisma.reactorPost.findUniqueOrThrow({
                where: { id: input.entityId },
            });
            masterPostId = input.entityId;
        }
        else {
            commentId = input.id;
        }
        const rawComments = await this.prisma.$queryRaw `
            SELECT
                comments.id,

                comments.post_id,

                comments.path,
                comments.internal_number,

                COUNT(DISTINCT rating_likes.id)::INT AS rating_likes,
                COUNT(DISTINCT rating_dislikes.id)::INT AS rating_dislikes,
                rating_status.type AS rating_status,

                comments.author_id,
                comments.is_anonymous,
                comments.anonimity_reason,

                COUNT(DISTINCT children.id)::INT AS children_count,

                comments.delete_reason,

                comments.created_at,
                comments.updated_at,
                comments.deleted_at

            FROM reactor_comments comments

            LEFT JOIN reactor_comments children
                ON children.post_id = comments.post_id
                AND children.id <> comments.id
                AND children.path::ltree <@ comments.path::ltree

            LEFT JOIN reactor_ratings rating_likes
                ON rating_likes.entity_id = comments.id
                AND rating_likes.entity_type = 'comment'
                AND rating_likes.type = 'like'

            LEFT JOIN reactor_ratings rating_dislikes
                ON rating_dislikes.entity_id = comments.id
                AND rating_dislikes.entity_type = 'comment'
                AND rating_dislikes.type = 'dislike'

            LEFT JOIN reactor_ratings rating_status
                ON rating_status.entity_id = comments.id
                AND rating_status.entity_type = 'comment'
                AND rating_status.user_id = ${user.id}

            WHERE
                ${new library_1.Sql([
            masterPostId
                ? `comments.post_id = '${masterPostId}'`
                : `comments.id = '${commentId}'`,
        ], [])}

            GROUP BY
                comments.id,
                rating_status.type

            ORDER BY path ASC
        `;
        console.dir({ rawComments }, { depth: null });
        const users = await this.prisma.user.findMany({
            where: {
                id: {
                    in: rawComments.map((c) => c.author_id),
                },
            },
            include: {
                name: true,
                image: true,
            },
        });
        const userMap = new Map(users.map((u) => [u.id, u]));
        const commentBodies = await this.prisma.reactorComment.findMany({
            where: {
                id: {
                    in: rawComments.map((c) => c.id),
                },
            },
            include: {
                body: true,
            },
        });
        const commentBodyMap = new Map(commentBodies.map((c) => [c.id, c.body]));
        const comments = rawComments.map((c) => {
            const user = userMap.get(c.author_id);
            const body = commentBodyMap.get(c.id);
            return {
                id: c.id,
                path: c.path,
                internalNumber: c.internal_number,
                author: c.is_anonymous
                    ? null
                    : {
                        id: user.id,
                        email: user.email,
                        name: user.name,
                        image: user.image?.url ?? null,
                    },
                isAnonymous: c.is_anonymous,
                anonimityReason: c.anonimity_reason,
                rating: {
                    likes: c.rating_likes,
                    dislikes: c.rating_dislikes,
                    status: c.rating_status,
                },
                body: c.deleted_at ? null : body,
                childrenCount: c.children_count,
                deleteReason: c.delete_reason,
                createdAt: c.created_at,
                updatedAt: c.updated_at,
                deletedAt: c.deleted_at,
            };
        });
        return comments;
    }
    async getNextCommentInternalNumber(postId) {
        const { internalNumber } = await this.prisma.reactorPostInternalNumber.upsert({
            where: { postId },
            update: { internalNumber: { increment: 1 } },
            create: { postId, internalNumber: 1 },
        });
        return internalNumber;
    }
    async createComment(dto, user) {
        const { entityType, entityId, body } = dto;
        if (entityType === "post") {
            const comment = await this.createPostComment({
                postId: entityId,
                body,
            }, user);
            return { id: comment.id };
        }
        if (entityType === "comment") {
            const comment = await this.createCommentComment({
                commentId: entityId,
                body,
            }, user);
            return { id: comment.id };
        }
        throw new Error("Impossible");
    }
    async createPostComment(dto, user) {
        const post = await this.prisma.reactorPost.findUnique({
            where: { id: dto.postId },
        });
        if (!post) {
            throw new common_1.NotFoundException(...(0, errors_1.getError)("post_not_found"));
        }
        const internalNumber = await this.getNextCommentInternalNumber(post.id);
        return await this.prisma.reactorComment.create({
            data: {
                postId: post.id,
                internalNumber,
                path: internalNumber.toString(),
                authorId: user.id,
                body: {
                    create: dto.body.map((b) => ({
                        key: "body",
                        value: b.value,
                        locale: b.locale,
                    })),
                },
            },
        });
    }
    async createCommentComment(dto, user) {
        const parentComment = await this.prisma.reactorComment.findUnique({
            where: { id: dto.commentId },
        });
        if (!parentComment) {
            throw new common_1.NotFoundException(...(0, errors_1.getError)("comment_not_found"));
        }
        const internalNumber = await this.getNextCommentInternalNumber(parentComment.postId);
        return await this.prisma.reactorComment.create({
            data: {
                postId: parentComment.postId,
                internalNumber,
                path: `${parentComment.path}.${internalNumber}`,
                authorId: user.id,
                body: {
                    create: dto.body.map((b) => ({
                        key: "body",
                        value: b.value,
                        locale: b.locale,
                    })),
                },
            },
        });
    }
    async updateComment(input, user) {
        const comment = await this.prisma.reactorComment.findUnique({
            where: { id: input.id },
        });
        if (!comment) {
            throw new common_1.NotFoundException(...(0, errors_1.getError)("comment_not_found"));
        }
        if (user.role !== "admin" && user.id !== comment.authorId) {
            throw new common_1.ForbiddenException(...(0, errors_1.getError)("must_be_admin_or_author"));
        }
        await this.prisma.$transaction(async (trx) => {
            await trx.reactorComment.update({
                where: { id: input.id },
                data: {
                    body: input.body && {
                        deleteMany: {},
                    },
                },
            });
            await trx.reactorComment.update({
                where: { id: input.id },
                data: {
                    body: input.body && {
                        create: input.body.map((b) => ({
                            key: "body",
                            value: b.value,
                            locale: b.locale,
                        })),
                    },
                },
            });
        });
        return true;
    }
    async updateCommentRating(input, user) {
        const comment = await this.prisma.reactorComment.findUnique({
            where: { id: input.id },
        });
        if (!comment) {
            throw new common_1.NotFoundException(...(0, errors_1.getError)("comment_not_found"));
        }
        const existingRating = await this.prisma.reactorRating.findFirst({
            where: {
                userId: user.id,
                entityType: "comment",
                entityId: comment.id,
            },
        });
        let newStatus = null;
        if (existingRating) {
            if (existingRating.type === input.type) {
                await this.prisma.$transaction(async (trx) => {
                    await trx.reactorRating.delete({
                        where: { id: existingRating.id },
                    });
                    await this.ratingService.deleteRelativeUserRating({
                        sourceUserId: user.id,
                        targetUserId: comment.authorId,
                        entityType: "comment",
                        entityId: comment.id,
                    }, trx);
                });
            }
            else {
                await this.prisma.$transaction(async (trx) => {
                    await trx.reactorRating.update({
                        where: { id: existingRating.id },
                        data: {
                            type: input.type,
                        },
                    });
                    await this.ratingService.upsertRelativeUserRating({
                        sourceUserId: user.id,
                        targetUserId: comment.authorId,
                        entityType: "comment",
                        entityId: comment.id,
                        value: input.type === "like" ? 1 : -1,
                    }, trx);
                });
                newStatus = input.type;
            }
        }
        else {
            await this.prisma.$transaction(async (trx) => {
                await trx.reactorRating.create({
                    data: {
                        userId: user.id,
                        entityId: comment.id,
                        entityType: "comment",
                        type: input.type,
                    },
                });
                await this.ratingService.upsertRelativeUserRating({
                    sourceUserId: user.id,
                    targetUserId: comment.authorId,
                    entityType: "comment",
                    entityId: comment.id,
                    value: input.type === "like" ? 1 : -1,
                }, trx);
            });
            newStatus = input.type;
        }
        const [likes, dislikes] = await Promise.all([
            this.prisma.reactorRating.count({
                where: {
                    entityType: "comment",
                    entityId: comment.id,
                    type: "like",
                },
            }),
            this.prisma.reactorRating.count({
                where: {
                    entityType: "comment",
                    entityId: comment.id,
                    type: "dislike",
                },
            }),
        ]);
        return {
            likes,
            dislikes,
            status: newStatus,
        };
    }
    async anonimifyComment(input, user) {
        const comment = await this.prisma.reactorComment.findUnique({
            where: { id: input.id },
        });
        if (!comment) {
            throw new common_1.NotFoundException(...(0, errors_1.getError)("comment_not_found"));
        }
        if (user.role !== "admin" && user.id !== comment.authorId) {
            throw new common_1.ForbiddenException(...(0, errors_1.getError)("must_be_admin_or_author"));
        }
        await this.prisma.reactorComment.update({
            where: { id: input.id },
            data: {
                isAnonymous: true,
                anonimityReason: input.reason,
            },
        });
        return true;
    }
    async deleteComment(input, user) {
        const comment = await this.prisma.reactorComment.findUnique({
            where: { id: input.id },
        });
        if (!comment) {
            throw new common_1.NotFoundException(...(0, errors_1.getError)("comment_not_found"));
        }
        if (user.role !== "admin" && user.id !== comment.authorId) {
            throw new common_1.ForbiddenException(...(0, errors_1.getError)("must_be_admin_or_author"));
        }
        await this.prisma.reactorComment.update({
            where: { id: input.id },
            data: {
                deleteReason: input.reason,
                deletedAt: new Date(),
            },
        });
        return true;
    }
};
exports.ReactorCommentService = ReactorCommentService;
exports.ReactorCommentService = ReactorCommentService = ReactorCommentService_1 = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [prisma_service_1.PrismaService,
        rating_service_1.RatingService])
], ReactorCommentService);
//# sourceMappingURL=reactor-comment.service.js.map