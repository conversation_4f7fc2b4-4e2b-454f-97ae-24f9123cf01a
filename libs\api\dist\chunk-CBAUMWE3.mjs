import {
  __export
} from "./chunk-J5LGTIGS.mjs";

// src/consts.ts
var consts_exports = {};
__export(consts_exports, {
  ALLOWED_IMAGE_FILE_TYPES: () => ALLOWED_IMAGE_FILE_TYPES,
  MAX_IMAGE_FILE_SIZE: () => MAX_IMAGE_FILE_SIZE,
  PAGE_SIZE: () => PAGE_SIZE
});
var PAGE_SIZE = 20;
var ALLOWED_IMAGE_FILE_TYPES = ["image/jpeg", "image/png", "image/webp"];
var MAX_IMAGE_FILE_SIZE = 5 * 1024 * 1024;

// src/common.ts
var common_exports = {};
__export(common_exports, {
  FormDataToObject: () => FormDataToObject,
  ImageSchema: () => ImageSchema,
  ImagesSchema: () => ImagesSchema,
  JsonStringToObject: () => JsonStringToObject,
  LocalizationLocaleSchema: () => LocalizationLocaleSchema,
  LocalizationLocalesSchema: () => LocalizationLocalesSchema,
  LocalizationSchema: () => LocalizationSchema,
  LocalizationsSchema: () => LocalizationsSchema,
  ObjectWithIdSchema: () => ObjectWithIdSchema,
  PaginationSchema: () => PaginationSchema,
  WebsiteLocaleSchema: () => WebsiteLocaleSchema,
  createdAt: () => createdAt,
  deletedAt: () => deletedAt,
  email: () => email,
  id: () => id,
  idOrNull: () => idOrNull,
  imageUrl: () => imageUrl,
  maybeImageUrl: () => maybeImageUrl,
  pagination: () => pagination,
  parseInput: () => parseInput,
  parseUnknown: () => parseUnknown,
  query: () => query,
  searchIds: () => searchIds,
  searchQuery: () => searchQuery,
  stringToDate: () => stringToDate,
  updatedAt: () => updatedAt,
  url: () => url
});
import { z } from "zod";
var id = z.string().nanoid();
var idOrNull = id.nullable().default(null);
var url = z.string().url();
var email = z.string().email();
var query = z.string().nonempty();
var imageUrl = z.string().nonempty();
var maybeImageUrl = imageUrl.nullable();
var createdAt = z.date();
var updatedAt = z.date();
var deletedAt = z.date().nullable();
var searchIds = z.array(id).min(1);
var searchQuery = z.string().nonempty();
var stringToDate = z.union([z.number(), z.string(), z.date()]).pipe(z.coerce.date());
function JsonStringToObject(schema) {
  return z.string().transform((value) => JSON.parse(value)).pipe(z.object(schema));
}
function FormDataToObject(schema) {
  return z.object({
    data: JsonStringToObject(schema)
  });
}
var ObjectWithIdSchema = z.object({ id });
var WebsiteLocaleSchema = z.enum(["en", "ru"]);
var LocalizationLocaleSchema = z.enum(["en", "ru"]);
var LocalizationLocalesSchema = z.array(LocalizationLocaleSchema).min(1);
var LocalizationSchema = z.object({
  locale: LocalizationLocaleSchema,
  value: z.string().nonempty()
});
var LocalizationsSchema = z.array(LocalizationSchema);
var ImageSchema = z.object({
  id,
  url: z.string(),
  createdAt: stringToDate,
  updatedAt: stringToDate
});
var ImagesSchema = z.array(ImageSchema);
var pagination = {
  offset: z.coerce.number().int().default(0),
  limit: z.coerce.number().int().positive().max(100).default(PAGE_SIZE),
  page: z.coerce.number().int().positive().default(1),
  size: z.coerce.number().int().positive().max(100).default(PAGE_SIZE)
};
var PaginationSchema = z.object({
  page: pagination.page,
  size: pagination.size
}).default({
  page: 1,
  size: PAGE_SIZE
});
function parseInput(schema, value) {
  return schema.parse(value);
}
function parseUnknown(schema, value) {
  return schema.parse(value);
}

// src/auth.ts
var auth_exports = {};
__export(auth_exports, {
  SendOtpInputSchema: () => SendOtpInputSchema,
  SendOtpOutputSchema: () => SendOtpOutputSchema,
  SigninInputSchema: () => SigninInputSchema,
  SignupInputSchema: () => SignupInputSchema,
  SuccessfulOutputSchema: () => SuccessfulOutputSchema,
  otp: () => otp
});
import { z as z3 } from "zod";

// src/user.ts
var user_exports = {};
__export(user_exports, {
  CreateUserTitleInputSchema: () => CreateUserTitleInputSchema,
  GetMeOutputSchema: () => GetMeOutputSchema,
  GetUserNoteInputSchema: () => GetUserNoteInputSchema,
  GetUserNoteOutputSchema: () => GetUserNoteOutputSchema,
  GetUserOutputSchema: () => GetUserOutputSchema,
  GetUserTitlesInputSchema: () => GetUserTitlesInputSchema,
  GetUserTitlesOutputSchema: () => GetUserTitlesOutputSchema,
  GetUsersInputSchema: () => GetUsersInputSchema,
  GetUsersOutputSchema: () => GetUsersOutputSchema,
  SimpleUserSchema: () => SimpleUserSchema,
  UpdateUserInputSchema: () => UpdateUserInputSchema,
  UpdateUserNoteInputSchema: () => UpdateUserNoteInputSchema,
  UpdateUserTitleInputSchema: () => UpdateUserTitleInputSchema,
  UserRoleSchema: () => UserRoleSchema,
  userDescription: () => userDescription,
  userImage: () => userImage,
  userName: () => userName,
  userNoteText: () => userNoteText,
  userTitleColor: () => userTitleColor,
  userTitleIsActive: () => userTitleIsActive,
  userTitleName: () => userTitleName
});
import { z as z2 } from "zod";
var userName = LocalizationsSchema;
var userDescription = LocalizationsSchema;
var userImage = imageUrl.nullable();
var userTitleName = LocalizationsSchema.min(1);
var userTitleIsActive = z2.boolean();
var userTitleColor = z2.string().nonempty().nullable();
var userNoteText = z2.string().nonempty();
var UserRoleSchema = z2.enum([
  "admin",
  "moderator",
  "user"
]);
var SimpleUserSchema = z2.object({
  id,
  email,
  name: userName,
  image: userImage
});
var GetMeOutputSchema = z2.object({
  id,
  email,
  role: UserRoleSchema,
  name: userName,
  description: userDescription,
  image: imageUrl.nullable(),
  createdAt,
  updatedAt
});
var GetUsersInputSchema = z2.object({
  pagination: PaginationSchema,
  ids: searchIds,
  query: searchQuery
}).partial();
var GetUserOutputSchema = z2.object({
  id,
  email,
  role: UserRoleSchema,
  name: userName,
  description: userDescription,
  image: userImage,
  createdAt,
  updatedAt,
  deletedAt: deletedAt.optional()
});
var GetUsersOutputSchema = z2.array(GetUserOutputSchema);
var UpdateUserInputSchema = z2.object({
  id,
  name: userName.optional(),
  description: userDescription.optional()
});
var CreateUserTitleInputSchema = z2.object({
  userId: id,
  name: userTitleName,
  isActive: userTitleIsActive,
  color: userTitleColor
});
var UpdateUserTitleInputSchema = z2.object({
  id,
  name: userTitleName.optional(),
  isActive: userTitleIsActive.optional(),
  color: userTitleColor.optional()
});
var GetUserTitlesInputSchema = z2.object({
  userId: id,
  ids: searchIds.optional(),
  isActive: userTitleIsActive.optional()
});
var GetUserTitlesOutputSchema = z2.array(
  z2.object({
    id,
    userId: id,
    name: userTitleName,
    isActive: userTitleIsActive,
    color: userTitleColor,
    createdAt,
    updatedAt,
    deletedAt: deletedAt.optional()
  })
);
var GetUserNoteInputSchema = z2.object({
  userId: id
});
var GetUserNoteOutputSchema = z2.object({
  text: userNoteText.nullable()
});
var UpdateUserNoteInputSchema = z2.object({
  userId: id,
  text: userNoteText.nullable()
});

// src/auth.ts
var otp = z3.string().nonempty().length(6);
var SendOtpInputSchema = z3.object({
  email
});
var SendOtpOutputSchema = z3.object({
  isSent: z3.boolean()
});
var SignupInputSchema = z3.object({
  referrerId: id.nullable(),
  email,
  otp
});
var SigninInputSchema = z3.object({
  email,
  otp
});
var SuccessfulOutputSchema = z3.object({
  id,
  email,
  role: UserRoleSchema
});

// src/commune.ts
var commune_exports = {};
__export(commune_exports, {
  CommuneInvitationStatusSchema: () => CommuneInvitationStatusSchema,
  CommuneJoinRequestStatusSchema: () => CommuneJoinRequestStatusSchema,
  CommuneMemberTypeSchema: () => CommuneMemberTypeSchema,
  CreateCommuneInputSchema: () => CreateCommuneInputSchema,
  CreateCommuneInvitationInputSchema: () => CreateCommuneInvitationInputSchema,
  CreateCommuneJoinRequestInputSchema: () => CreateCommuneJoinRequestInputSchema,
  CreateCommuneMemberInputSchema: () => CreateCommuneMemberInputSchema,
  GetCommuneInvitationsInputSchema: () => GetCommuneInvitationsInputSchema,
  GetCommuneInvitationsOutputSchema: () => GetCommuneInvitationsOutputSchema,
  GetCommuneJoinRequestsInputSchema: () => GetCommuneJoinRequestsInputSchema,
  GetCommuneJoinRequestsOutputSchema: () => GetCommuneJoinRequestsOutputSchema,
  GetCommuneMemberOutputSchema: () => GetCommuneMemberOutputSchema,
  GetCommuneMembersInputSchema: () => GetCommuneMembersInputSchema,
  GetCommuneMembersOutputSchema: () => GetCommuneMembersOutputSchema,
  GetCommuneOutputSchema: () => GetCommuneOutputSchema,
  GetCommunesInputSchema: () => GetCommunesInputSchema,
  GetCommunesOutputSchema: () => GetCommunesOutputSchema,
  TransferHeadStatusInputSchema: () => TransferHeadStatusInputSchema,
  UpdateCommuneInputSchema: () => UpdateCommuneInputSchema,
  communeDescription: () => communeDescription,
  communeMemberActorType: () => communeMemberActorType,
  communeMemberName: () => communeMemberName,
  communeName: () => communeName
});
import { z as z4 } from "zod";
var CommuneMemberTypeSchema = z4.enum(["user"]);
var communeName = LocalizationsSchema.min(1);
var communeDescription = LocalizationsSchema;
var communeMemberActorType = CommuneMemberTypeSchema;
var communeMemberName = z4.union([userName, communeName]);
var TransferHeadStatusInputSchema = z4.object({
  communeId: id,
  newHeadUserId: id
});
var GetCommunesInputSchema = z4.object({
  pagination: PaginationSchema,
  ids: searchIds,
  query: searchQuery,
  userId: id
}).partial();
var GetCommuneOutputSchema = z4.object({
  id,
  name: communeName,
  description: communeDescription,
  headMember: z4.object({
    actorType: communeMemberActorType,
    actorId: id,
    name: communeMemberName,
    image: maybeImageUrl
  }),
  memberCount: z4.number().int().positive(),
  image: maybeImageUrl,
  createdAt,
  updatedAt,
  deletedAt: deletedAt.optional()
});
var GetCommunesOutputSchema = z4.array(GetCommuneOutputSchema);
var CreateCommuneInputSchema = z4.object({
  headUserId: id.optional(),
  name: communeName,
  description: communeDescription
});
var UpdateCommuneInputSchema = z4.object({
  id,
  name: communeName.optional(),
  description: communeDescription.optional()
});
var GetCommuneMembersInputSchema = z4.object({
  pagination: PaginationSchema,
  communeId: id
});
var GetCommuneMemberOutputSchema = z4.object({
  id,
  actorType: communeMemberActorType,
  actorId: id,
  name: communeMemberName,
  image: maybeImageUrl,
  createdAt,
  deletedAt: deletedAt.nullable()
});
var GetCommuneMembersOutputSchema = z4.array(GetCommuneMemberOutputSchema);
var CreateCommuneMemberInputSchema = z4.object({
  communeId: id,
  userId: id
});
var CommuneInvitationStatusSchema = z4.enum(["pending", "accepted", "rejected", "expired"]);
var GetCommuneInvitationsInputSchema = z4.object({
  pagination: PaginationSchema,
  communeId: id.optional()
});
var GetCommuneInvitationsOutputSchema = z4.array(
  z4.object({
    id,
    communeId: id,
    userId: id,
    status: CommuneInvitationStatusSchema,
    createdAt: z4.date(),
    updatedAt: z4.date()
  })
);
var CreateCommuneInvitationInputSchema = z4.object({
  communeId: id,
  userId: id
});
var CommuneJoinRequestStatusSchema = z4.enum(["pending", "accepted", "rejected"]);
var GetCommuneJoinRequestsInputSchema = z4.object({
  pagination: PaginationSchema,
  communeId: id.optional()
});
var GetCommuneJoinRequestsOutputSchema = z4.array(
  z4.object({
    id,
    communeId: id,
    userId: id,
    status: CommuneJoinRequestStatusSchema,
    createdAt: z4.date(),
    updatedAt: z4.date()
  })
);
var CreateCommuneJoinRequestInputSchema = z4.object({
  communeId: id,
  userId: id
});

// src/reactor.ts
var reactor_exports = {};
__export(reactor_exports, {
  AnonimifyCommentInputSchema: () => AnonimifyCommentInputSchema,
  CommentEntityTypeSchema: () => CommentEntityTypeSchema,
  CreateCommentInputSchema: () => CreateCommentInputSchema,
  CreateCommunityInputSchema: () => CreateCommunityInputSchema,
  CreateHubInputSchema: () => CreateHubInputSchema,
  CreateLensInputSchema: () => CreateLensInputSchema,
  CreatePostInputSchema: () => CreatePostInputSchema,
  DeleteCommentInputSchema: () => DeleteCommentInputSchema,
  DeletePostInputSchema: () => DeletePostInputSchema,
  GetCommentsInputSchema: () => GetCommentsInputSchema,
  GetCommentsOutputSchema: () => GetCommentsOutputSchema,
  GetCommunitiesInputSchema: () => GetCommunitiesInputSchema,
  GetCommunitiesOutputSchema: () => GetCommunitiesOutputSchema,
  GetHubsInputSchema: () => GetHubsInputSchema,
  GetHubsOutputSchema: () => GetHubsOutputSchema,
  GetPostOutputSchema: () => GetPostOutputSchema,
  GetPostsInputSchema: () => GetPostsInputSchema,
  GetPostsOutputSchema: () => GetPostsOutputSchema,
  PostUsefulnessSchema: () => PostUsefulnessSchema,
  RatingSchema: () => RatingSchema,
  RatingTypeSchema: () => RatingTypeSchema,
  UpdateCommentInputSchema: () => UpdateCommentInputSchema,
  UpdateCommentRatingInputSchema: () => UpdateCommentRatingInputSchema,
  UpdateCommentRatingOutputSchema: () => UpdateCommentRatingOutputSchema,
  UpdateCommunityInputSchema: () => UpdateCommunityInputSchema,
  UpdateHubInputSchema: () => UpdateHubInputSchema,
  UpdateLensInputSchema: () => UpdateLensInputSchema,
  UpdatePostInputSchema: () => UpdatePostInputSchema,
  UpdatePostRatingInputSchema: () => UpdatePostRatingInputSchema,
  UpdatePostRatingOutputSchema: () => UpdatePostRatingOutputSchema,
  UpdatePostUsefulnessInputSchema: () => UpdatePostUsefulnessInputSchema,
  UpdatePostUsefulnessOutputSchema: () => UpdatePostUsefulnessOutputSchema,
  commentBody: () => commentBody,
  communityDescription: () => communityDescription,
  communityImage: () => communityImage,
  communityName: () => communityName,
  hubDescription: () => hubDescription,
  hubImage: () => hubImage,
  hubName: () => hubName,
  lensCode: () => lensCode,
  lensName: () => lensName,
  postBody: () => postBody,
  postTitle: () => postTitle,
  postUsefulness: () => postUsefulness
});
import { z as z6 } from "zod";

// src/tag.ts
var tag_exports = {};
__export(tag_exports, {
  CreateTagInputSchema: () => CreateTagInputSchema,
  GetTagsInputSchema: () => GetTagsInputSchema,
  GetTagsOutputSchema: () => GetTagsOutputSchema,
  UpdateTagInputSchema: () => UpdateTagInputSchema,
  tagName: () => tagName
});
import { z as z5 } from "zod";
var tagName = LocalizationsSchema.min(1);
var GetTagsInputSchema = z5.object({
  pagination: PaginationSchema,
  ids: searchIds,
  query: searchQuery
}).partial();
var GetTagsOutputSchema = z5.array(
  z5.object({
    id,
    name: tagName,
    deletedAt: deletedAt.optional()
  })
);
var CreateTagInputSchema = z5.object({
  name: tagName
});
var UpdateTagInputSchema = z5.object({
  id,
  name: tagName
});

// src/reactor.ts
var RatingTypeSchema = z6.enum(["like", "dislike"]);
var RatingSchema = z6.object({
  likes: z6.number().int().nonnegative(),
  dislikes: z6.number().int().nonnegative(),
  status: RatingTypeSchema.nullable()
});
var hubName = LocalizationsSchema.min(1);
var hubDescription = LocalizationsSchema.min(1);
var hubImage = maybeImageUrl;
var communityName = LocalizationsSchema.min(1);
var communityDescription = LocalizationsSchema.min(1);
var communityImage = maybeImageUrl;
var postUsefulness = z6.number().int().min(0).max(10);
var PostUsefulnessSchema = z6.object({
  value: postUsefulness.nullable(),
  count: z6.number().int().nonnegative(),
  totalValue: z6.number().min(0).max(10).nullable()
});
var postTitle = LocalizationsSchema.min(1);
var postBody = LocalizationsSchema.min(1);
var GetPostOutputSchema = z6.object({
  id,
  hub: z6.object({
    id,
    name: hubName,
    image: hubImage
  }).nullable(),
  community: z6.object({
    id,
    name: communityName,
    image: communityImage
  }).nullable(),
  author: SimpleUserSchema,
  rating: RatingSchema,
  usefulness: PostUsefulnessSchema,
  title: postTitle,
  body: postBody,
  tags: z6.array(
    z6.object({
      id,
      name: tagName
    })
  ),
  createdAt,
  updatedAt,
  deletedAt: deletedAt.optional()
});
var GetPostsInputSchema = z6.object({
  pagination: PaginationSchema,
  id: id.optional(),
  lensId: id.nullable()
});
var GetPostsOutputSchema = z6.array(GetPostOutputSchema);
var CreatePostInputSchema = z6.object({
  hubId: id.nullable(),
  communityId: id.nullable(),
  title: postTitle,
  body: postBody,
  tagIds: z6.array(id)
});
var UpdatePostInputSchema = z6.object({
  id,
  title: postTitle.optional(),
  body: postBody.optional(),
  tagIds: z6.array(id).optional()
});
var DeletePostInputSchema = z6.object({
  id,
  reason: z6.string().nonempty().nullable()
});
var UpdatePostRatingInputSchema = z6.object({
  id,
  type: RatingTypeSchema
});
var UpdatePostRatingOutputSchema = RatingSchema;
var UpdatePostUsefulnessInputSchema = z6.object({
  id,
  value: postUsefulness.nullable()
});
var UpdatePostUsefulnessOutputSchema = PostUsefulnessSchema;
var CommentEntityTypeSchema = z6.enum(["post", "comment"]);
var commentBody = LocalizationsSchema.min(1);
var GetCommentsInputSchema = z6.union([
  z6.object({
    id,
    entityType: z6.never().optional(),
    entityId: z6.never().optional()
  }),
  z6.object({
    id: z6.never().optional(),
    entityType: CommentEntityTypeSchema,
    entityId: id
  })
]);
var GetCommentsOutputSchema = z6.array(
  z6.object({
    id,
    path: z6.string().nonempty(),
    author: SimpleUserSchema.nullable(),
    isAnonymous: z6.boolean(),
    anonimityReason: z6.string().nonempty().nullable(),
    rating: RatingSchema,
    body: commentBody.nullable(),
    childrenCount: z6.number().int().nonnegative(),
    deleteReason: z6.string().nonempty().nullable(),
    createdAt,
    updatedAt,
    deletedAt
  })
);
var CreateCommentInputSchema = z6.object({
  entityType: CommentEntityTypeSchema,
  entityId: id,
  body: commentBody
});
var UpdateCommentInputSchema = z6.object({
  id,
  body: commentBody.optional()
});
var DeleteCommentInputSchema = z6.object({
  id,
  reason: z6.string().nonempty().nullable()
});
var UpdateCommentRatingInputSchema = z6.object({
  id,
  type: RatingTypeSchema
});
var UpdateCommentRatingOutputSchema = RatingSchema;
var AnonimifyCommentInputSchema = z6.object({
  id,
  reason: z6.string().nonempty().nullable()
});
var lensName = z6.string().nonempty();
var lensCode = z6.string().nonempty();
var CreateLensInputSchema = z6.object({
  name: lensName,
  code: lensCode
});
var UpdateLensInputSchema = z6.object({
  id,
  name: lensName.optional(),
  code: lensCode.optional()
});
var GetHubsInputSchema = z6.object({
  pagination: PaginationSchema,
  ids: searchIds,
  query: searchQuery
}).partial();
var GetHubsOutputSchema = z6.array(
  z6.object({
    id,
    headUser: SimpleUserSchema,
    image: hubImage,
    name: hubName,
    description: hubDescription,
    createdAt,
    updatedAt,
    deletedAt: deletedAt.optional()
  })
);
var CreateHubInputSchema = z6.object({
  headUserId: id.nullable(),
  name: hubName,
  description: hubDescription
});
var UpdateHubInputSchema = z6.object({
  id,
  name: hubName.optional(),
  description: hubDescription.optional()
});
var GetCommunitiesInputSchema = z6.object({
  pagination: PaginationSchema,
  ids: searchIds,
  query: searchQuery,
  hubId: id
}).partial();
var GetCommunitiesOutputSchema = z6.array(
  z6.object({
    id,
    hub: z6.object({
      id,
      name: hubName,
      image: hubImage
    }).nullable(),
    headUser: SimpleUserSchema,
    image: communityImage,
    name: communityName,
    description: communityDescription,
    createdAt,
    updatedAt,
    deletedAt: deletedAt.optional()
  })
);
var CreateCommunityInputSchema = z6.object({
  hubId: id.nullable(),
  headUserId: id.nullable(),
  name: communityName,
  description: communityDescription
});
var UpdateCommunityInputSchema = z6.object({
  id,
  name: communityName.optional(),
  description: communityDescription.optional()
});

// src/rating.ts
var rating_exports = {};
__export(rating_exports, {
  CreateUserFeedbackInputSchema: () => CreateUserFeedbackInputSchema,
  GetKarmaPointsInputSchema: () => GetKarmaPointsInputSchema,
  GetKarmaPointsOutputSchema: () => GetKarmaPointsOutputSchema,
  GetUserFeedbacksInputSchema: () => GetUserFeedbacksInputSchema,
  GetUserFeedbacksOutputSchema: () => GetUserFeedbacksOutputSchema,
  GetUserSummaryInputSchema: () => GetUserSummaryInputSchema,
  GetUserSummaryOutputSchema: () => GetUserSummaryOutputSchema,
  SpendKarmaPointInputSchema: () => SpendKarmaPointInputSchema,
  karmaPointComment: () => karmaPointComment,
  karmaPointQuantity: () => karmaPointQuantity,
  userFeedbackText: () => userFeedbackText,
  userFeedbackValue: () => userFeedbackValue
});
import { z as z7 } from "zod";
var karmaPointQuantity = z7.number().int();
var karmaPointComment = LocalizationsSchema.min(1);
var GetKarmaPointsInputSchema = z7.object({
  pagination: PaginationSchema,
  userId: id
});
var GetKarmaPointsOutputSchema = z7.array(
  z7.object({
    id,
    author: SimpleUserSchema,
    quantity: karmaPointQuantity,
    comment: karmaPointComment
  })
);
var SpendKarmaPointInputSchema = z7.object({
  sourceUserId: id,
  targetUserId: id,
  quantity: karmaPointQuantity,
  comment: karmaPointComment
});
var userFeedbackValue = z7.number().int().min(0).max(10);
var userFeedbackText = LocalizationsSchema.min(1);
var GetUserFeedbacksInputSchema = z7.object({
  pagination: PaginationSchema,
  userId: id
});
var GetUserFeedbacksOutputSchema = z7.array(
  z7.object({
    id,
    author: SimpleUserSchema.nullable(),
    isAnonymous: z7.boolean(),
    value: userFeedbackValue,
    text: userFeedbackText
  })
);
var CreateUserFeedbackInputSchema = z7.object({
  sourceUserId: id,
  targetUserId: id,
  value: userFeedbackValue,
  isAnonymous: z7.boolean(),
  text: userFeedbackText
});
var GetUserSummaryInputSchema = z7.object({
  userId: id
});
var GetUserSummaryOutputSchema = z7.object({
  rating: z7.number().int(),
  karma: z7.number().int(),
  rate: z7.number().min(0).max(10).nullable()
});

export {
  consts_exports,
  common_exports,
  user_exports,
  auth_exports,
  commune_exports,
  tag_exports,
  reactor_exports,
  rating_exports
};
//# sourceMappingURL=chunk-CBAUMWE3.mjs.map