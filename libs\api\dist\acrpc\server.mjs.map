{"version": 3, "sources": ["../../src/acrpc/server.ts"], "sourcesContent": ["import type { Request, Response } from \"express\";\nimport type { DeepPartial } from \"../types\";\nimport type {\n    Method,\n    Schema,\n    SchemaEndpoint,\n    SchemaRoute,\n    Transformer,\n} from \"./core\";\n\nimport { z } from \"zod\";\nimport express, { Router } from \"express\";\nimport {\n    log,\n    dir,\n    kebabTransformer,\n    jsonTransformer,\n} from \"./core\";\n\ntype MaybePromise<T> = T | Promise<T>;\n\nexport type SchemaRouteHandlers<TSchemaEndpoint extends SchemaRoute, TMetadata = never> = {\n    [M in keyof TSchemaEndpoint]: TSchemaEndpoint[M] extends SchemaEndpoint\n        ? (\n            input: TSchemaEndpoint[M][\"input\"] extends z.ZodType\n                ? z.infer<TSchemaEndpoint[M][\"input\"]>\n                : TSchemaEndpoint[M][\"input\"] extends null\n                    ? never\n                    : unknown,\n\n            metadata:\n                | (\n                    TSchemaEndpoint[M][\"isMetadataUsed\"] extends false\n                    ? never\n                    : TMetadata\n                )\n                | (\n                    TSchemaEndpoint[M][\"isMetadataRequired\"] extends false\n                    ? null\n                    : never\n                ),\n\n                \n            rest: {\n                req: Request;\n                res: Response;\n            },\n        ) => MaybePromise<\n            TSchemaEndpoint[M][\"output\"] extends z.ZodType\n                ? z.infer<TSchemaEndpoint[M][\"output\"]>\n                : unknown\n        >\n        : TSchemaEndpoint[M] extends SchemaRoute\n            ? SchemaRouteHandlers<TSchemaEndpoint[M], TMetadata>\n            : never; \n};\n\nexport type Handlers<TSchema extends Schema, TMetadata = never> = {\n    [K in keyof TSchema]: TSchema[K] extends SchemaRoute\n        ? SchemaRouteHandlers<TSchema[K], TMetadata>\n        : TSchema[K] extends Schema\n            ? Handlers<TSchema[K], TMetadata>\n            : never;\n};\n\nexport type PartialHandlers<TSchema extends Schema, TMetadata = never> = DeepPartial<Handlers<TSchema, TMetadata>>;\n\nfunction isEndpoint(schemaEntry: unknown): schemaEntry is SchemaEndpoint {\n    return (\n        schemaEntry != null && typeof schemaEntry === \"object\" && (\n            (\n                \"input\" in schemaEntry\n                && (\n                    schemaEntry[\"input\"] instanceof z.ZodType\n                    || schemaEntry[\"input\"] === null\n                    || schemaEntry[\"input\"] === undefined\n                )\n            )\n            && (\n                \"output\" in schemaEntry\n                && (\n                    schemaEntry[\"output\"] instanceof z.ZodType\n                    || schemaEntry[\"output\"] === null\n                    || schemaEntry[\"output\"] === undefined\n                )\n            )\n        )\n    );\n}\n\ntype InputParseResult =\n    | { success: true, data: unknown }\n    | { success: false, status: number, response: unknown };\n\nfunction getInput(\n    req: Request,\n    inputSchema: z.ZodType | null | undefined,\n    transformer: Transformer,\n): InputParseResult {\n    let body: unknown = null;\n\n    dir({\n        method: req.method,\n        query: req.query,\n        body: req.body,\n    });\n\n    if (req.method === \"GET\") {\n        body = req.query.__body\n            ? decodeURIComponent(req.query.__body as string)\n            : null;\n\n        log(\"get\", { body });\n\n        if (inputSchema && !body) {\n            return {\n                success: false,\n                status: 400,\n                response: {\n                    error: \"No __body provided\",\n                },\n            };\n        }\n    }\n    else {\n        body = req.body;\n\n        log(\"non-get\", { body });\n\n        if (inputSchema && !body) {\n            return {\n                success: false,\n                status: 400,\n                response: {\n                    error: \"No body provided\",\n                },\n            };\n        }\n    }\n\n    log(\"before deserialize\", { body });\n\n    const rawInput = body ? transformer.deserialize(body as string) : null;\n\n    dir({ rawInput });\n\n    if (inputSchema) {\n        const schemaParseResult = inputSchema.safeParse(rawInput);\n\n        dir({ schemaParseResult });\n\n        if (schemaParseResult.success) {\n            return {\n                success: true,\n                data: schemaParseResult.data,\n            };\n        }\n\n        return {\n            success: false,\n            status: 400,\n            response: schemaParseResult.error.issues.map(issue => ({\n                path: issue.path,\n                message: issue.message,\n            })),\n        };\n    }\n\n    return {\n        success: true,\n        data: rawInput,\n    };\n}\n\nexport function createServer<\n    TSchema extends Schema,\n    TMetadata extends Record<string, any> = never,\n>(\n    schema: TSchema,\n    handlers: PartialHandlers<TSchema, TMetadata>,\n    options?: {\n        transformer?: Transformer;\n        getMetadata?: (req: Request) => TMetadata | null;\n    },\n) {\n    const transformer = options?.transformer ?? jsonTransformer;\n    const getMetadata = options?.getMetadata;\n    const router = Router();\n\n    const textMiddleware = express.text({ type: \"*/*\" });\n\n    function fillRouter(\n        schema: Record<string, any>,\n        handlers: Record<string, any>,\n        names: readonly string[],\n    ) {\n        log({ names });\n    \n        for (const [name, handler] of Object.entries(handlers) as [string, any][]) {\n            log({ name });\n\n            const schemaEntry = schema[name];\n    \n            if (isEndpoint(schemaEntry)) {\n                const path = [\"\", ...names].join(\"/\");\n                const method = name as Method;\n                const okStatus = method === \"post\" ? 201 : 200;\n    \n                log(`Registering ${method.toUpperCase()} ${path}...`);\n    \n                if (method !== \"get\") {\n                    router[method](path, textMiddleware);\n                }\n    \n                router[method](\n                    path,\n                    async (req, res) => {\n                        let metadata: TMetadata | null | undefined = undefined;\n\n                        const isMetadataUsed = schemaEntry.isMetadataUsed ?? true;\n                        const isMetadataRequired = schemaEntry.isMetadataRequired ?? true;\n\n                        dir({\n                            getMetadata,\n                            isMetadataUsed,\n                            isMetadataRequired,\n                        });\n\n                        if (isMetadataUsed) {\n                            metadata = getMetadata?.(req) ?? null;\n\n                            dir({ metadata });\n\n                            if (isMetadataRequired && !metadata) {\n                                return res.status(400).json({\n                                    error: \"Metadata cannot be parsed.\",\n                                });\n                            }\n                        }\n\n                        const inputParseResult = getInput(\n                            req,\n                            schemaEntry.input,\n                            transformer,\n                        );\n    \n                        dir({ inputParseResult });\n    \n                        if (!inputParseResult.success) {\n                            return res\n                                .status(inputParseResult.status)\n                                .json(inputParseResult.response);\n                        }\n    \n                        dir({ handlers });\n    \n                        const rawOutput = await handler(\n                            inputParseResult.data,\n                            metadata ?? null,\n                            {\n                                req,\n                                res,\n                            },\n                        );\n\n                        let output: unknown = null;\n\n                        if (schemaEntry.output !== null) {\n                            let parsedOutput: unknown = null;\n\n                            if (schemaEntry.output) {\n                                const outputParseResult = schemaEntry.output.safeParse(rawOutput);\n    \n                                dir({\n                                    rawOutput,\n                                    outputParseResult,\n                                });\n            \n                                if (outputParseResult.error) {\n                                    return res.status(500).json(outputParseResult.error);\n                                }\n\n                                parsedOutput = outputParseResult.data;\n                            }\n                            else {\n                                parsedOutput = rawOutput;\n                            }\n            \n                            const serializedOutput = transformer.serialize(parsedOutput);\n        \n                            dir({ serializedOutput });\n\n                            output = serializedOutput;\n                        }\n\n                        if (schemaEntry.cacheControl) {\n                            res.setHeader(\"Cache-Control\", schemaEntry.cacheControl);\n                        }\n\n                        res.status(okStatus);\n\n                        if (output != null) {\n                            res.send(output);\n                        }\n                        else {\n                            res.send(\"\");\n                        }\n                    },\n                );\n            }\n            else {\n                fillRouter(\n                    schema[name] as Schema,\n                    handlers[name],\n                    [...names, kebabTransformer.transform(name)],\n                );\n            }\n        }\n    }\n\n    fillRouter(schema, handlers, []);\n\n    function register(\n        handlers: PartialHandlers<TSchema, TMetadata>,\n    ) {\n        fillRouter(schema, handlers, []);\n    }\n\n    return {\n        router,\n        register,\n    }\n}\n"], "mappings": ";;;;;;;;;AAUA,SAAS,SAAS;AAClB,OAAO,WAAW,cAAc;AAwDhC,SAAS,WAAW,aAAqD;AACrE,SACI,eAAe,QAAQ,OAAO,gBAAgB,aAEtC,WAAW,gBAEP,YAAY,OAAO,aAAa,EAAE,WAC/B,YAAY,OAAO,MAAM,QACzB,YAAY,OAAO,MAAM,YAIhC,YAAY,gBAER,YAAY,QAAQ,aAAa,EAAE,WAChC,YAAY,QAAQ,MAAM,QAC1B,YAAY,QAAQ,MAAM;AAKjD;AAMA,SAAS,SACL,KACA,aACA,aACgB;AAChB,MAAI,OAAgB;AAEpB,MAAI;AAAA,IACA,QAAQ,IAAI;AAAA,IACZ,OAAO,IAAI;AAAA,IACX,MAAM,IAAI;AAAA,EACd,CAAC;AAED,MAAI,IAAI,WAAW,OAAO;AACtB,WAAO,IAAI,MAAM,SACX,mBAAmB,IAAI,MAAM,MAAgB,IAC7C;AAEN,QAAI,OAAO,EAAE,KAAK,CAAC;AAEnB,QAAI,eAAe,CAAC,MAAM;AACtB,aAAO;AAAA,QACH,SAAS;AAAA,QACT,QAAQ;AAAA,QACR,UAAU;AAAA,UACN,OAAO;AAAA,QACX;AAAA,MACJ;AAAA,IACJ;AAAA,EACJ,OACK;AACD,WAAO,IAAI;AAEX,QAAI,WAAW,EAAE,KAAK,CAAC;AAEvB,QAAI,eAAe,CAAC,MAAM;AACtB,aAAO;AAAA,QACH,SAAS;AAAA,QACT,QAAQ;AAAA,QACR,UAAU;AAAA,UACN,OAAO;AAAA,QACX;AAAA,MACJ;AAAA,IACJ;AAAA,EACJ;AAEA,MAAI,sBAAsB,EAAE,KAAK,CAAC;AAElC,QAAM,WAAW,OAAO,YAAY,YAAY,IAAc,IAAI;AAElE,MAAI,EAAE,SAAS,CAAC;AAEhB,MAAI,aAAa;AACb,UAAM,oBAAoB,YAAY,UAAU,QAAQ;AAExD,QAAI,EAAE,kBAAkB,CAAC;AAEzB,QAAI,kBAAkB,SAAS;AAC3B,aAAO;AAAA,QACH,SAAS;AAAA,QACT,MAAM,kBAAkB;AAAA,MAC5B;AAAA,IACJ;AAEA,WAAO;AAAA,MACH,SAAS;AAAA,MACT,QAAQ;AAAA,MACR,UAAU,kBAAkB,MAAM,OAAO,IAAI,YAAU;AAAA,QACnD,MAAM,MAAM;AAAA,QACZ,SAAS,MAAM;AAAA,MACnB,EAAE;AAAA,IACN;AAAA,EACJ;AAEA,SAAO;AAAA,IACH,SAAS;AAAA,IACT,MAAM;AAAA,EACV;AACJ;AAEO,SAAS,aAIZ,QACA,UACA,SAIF;AACE,QAAM,cAAc,SAAS,eAAe;AAC5C,QAAM,cAAc,SAAS;AAC7B,QAAM,SAAS,OAAO;AAEtB,QAAM,iBAAiB,QAAQ,KAAK,EAAE,MAAM,MAAM,CAAC;AAEnD,WAAS,WACLA,SACAC,WACA,OACF;AACE,QAAI,EAAE,MAAM,CAAC;AAEb,eAAW,CAAC,MAAM,OAAO,KAAK,OAAO,QAAQA,SAAQ,GAAsB;AACvE,UAAI,EAAE,KAAK,CAAC;AAEZ,YAAM,cAAcD,QAAO,IAAI;AAE/B,UAAI,WAAW,WAAW,GAAG;AACzB,cAAM,OAAO,CAAC,IAAI,GAAG,KAAK,EAAE,KAAK,GAAG;AACpC,cAAM,SAAS;AACf,cAAM,WAAW,WAAW,SAAS,MAAM;AAE3C,YAAI,eAAe,OAAO,YAAY,CAAC,IAAI,IAAI,KAAK;AAEpD,YAAI,WAAW,OAAO;AAClB,iBAAO,MAAM,EAAE,MAAM,cAAc;AAAA,QACvC;AAEA,eAAO,MAAM;AAAA,UACT;AAAA,UACA,OAAO,KAAK,QAAQ;AAChB,gBAAI,WAAyC;AAE7C,kBAAM,iBAAiB,YAAY,kBAAkB;AACrD,kBAAM,qBAAqB,YAAY,sBAAsB;AAE7D,gBAAI;AAAA,cACA;AAAA,cACA;AAAA,cACA;AAAA,YACJ,CAAC;AAED,gBAAI,gBAAgB;AAChB,yBAAW,cAAc,GAAG,KAAK;AAEjC,kBAAI,EAAE,SAAS,CAAC;AAEhB,kBAAI,sBAAsB,CAAC,UAAU;AACjC,uBAAO,IAAI,OAAO,GAAG,EAAE,KAAK;AAAA,kBACxB,OAAO;AAAA,gBACX,CAAC;AAAA,cACL;AAAA,YACJ;AAEA,kBAAM,mBAAmB;AAAA,cACrB;AAAA,cACA,YAAY;AAAA,cACZ;AAAA,YACJ;AAEA,gBAAI,EAAE,iBAAiB,CAAC;AAExB,gBAAI,CAAC,iBAAiB,SAAS;AAC3B,qBAAO,IACF,OAAO,iBAAiB,MAAM,EAC9B,KAAK,iBAAiB,QAAQ;AAAA,YACvC;AAEA,gBAAI,EAAE,UAAAC,UAAS,CAAC;AAEhB,kBAAM,YAAY,MAAM;AAAA,cACpB,iBAAiB;AAAA,cACjB,YAAY;AAAA,cACZ;AAAA,gBACI;AAAA,gBACA;AAAA,cACJ;AAAA,YACJ;AAEA,gBAAI,SAAkB;AAEtB,gBAAI,YAAY,WAAW,MAAM;AAC7B,kBAAI,eAAwB;AAE5B,kBAAI,YAAY,QAAQ;AACpB,sBAAM,oBAAoB,YAAY,OAAO,UAAU,SAAS;AAEhE,oBAAI;AAAA,kBACA;AAAA,kBACA;AAAA,gBACJ,CAAC;AAED,oBAAI,kBAAkB,OAAO;AACzB,yBAAO,IAAI,OAAO,GAAG,EAAE,KAAK,kBAAkB,KAAK;AAAA,gBACvD;AAEA,+BAAe,kBAAkB;AAAA,cACrC,OACK;AACD,+BAAe;AAAA,cACnB;AAEA,oBAAM,mBAAmB,YAAY,UAAU,YAAY;AAE3D,kBAAI,EAAE,iBAAiB,CAAC;AAExB,uBAAS;AAAA,YACb;AAEA,gBAAI,YAAY,cAAc;AAC1B,kBAAI,UAAU,iBAAiB,YAAY,YAAY;AAAA,YAC3D;AAEA,gBAAI,OAAO,QAAQ;AAEnB,gBAAI,UAAU,MAAM;AAChB,kBAAI,KAAK,MAAM;AAAA,YACnB,OACK;AACD,kBAAI,KAAK,EAAE;AAAA,YACf;AAAA,UACJ;AAAA,QACJ;AAAA,MACJ,OACK;AACD;AAAA,UACID,QAAO,IAAI;AAAA,UACXC,UAAS,IAAI;AAAA,UACb,CAAC,GAAG,OAAO,iBAAiB,UAAU,IAAI,CAAC;AAAA,QAC/C;AAAA,MACJ;AAAA,IACJ;AAAA,EACJ;AAEA,aAAW,QAAQ,UAAU,CAAC,CAAC;AAE/B,WAAS,SACLA,WACF;AACE,eAAW,QAAQA,WAAU,CAAC,CAAC;AAAA,EACnC;AAEA,SAAO;AAAA,IACH;AAAA,IACA;AAAA,EACJ;AACJ;", "names": ["schema", "handlers"]}