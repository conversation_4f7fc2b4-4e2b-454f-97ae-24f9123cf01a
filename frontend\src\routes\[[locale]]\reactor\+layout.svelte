<script lang="ts">
  import { LocaleSwitcher } from "$components";

  const i18n = {
    ru: {
      navGap: "mx-1",
      commune: "Коммуна",
      theLaw: "Право",
      rules: "Правила",
      newEnglish: "Новый английский",
      newCalendar: "Новый календарь",
      news: "Новости",
      users: "Пользователи",
      communes: "Коммуны",
      hubs: "Хабы",
      communities: "Сообщества",
      profile: "Профиль",
      feed: "Лента",
      hole: "Яма",
    },

    en: {
      navGap: "mx-2",
      commune: "Commune",
      theLaw: "The Law",
      rules: "Rules",
      newEnglish: "New English",
      newCalendar: "New Calendar",
      news: "News",
      users: "Users",
      communes: "Communes",
      hubs: "Hubs",
      communities: "Communities",
      profile: "Profile",
      feed: "Feed",
      hole: "Pit",
    },
  };

  const { children, data } = $props();
  const { locale, routeLocale, toLocaleHref } = $derived(data);

  const t = $derived(i18n[locale]);
</script>

<div class="page-wrapper">
  <nav class="navbar navbar-expand-lg sticky-top reactor-navbar">
    <div class="container">
      <a href={toLocaleHref("/")} class="navbar-brand py-0 ps-5">
        <img
          src="/images/full-v3-transparent-white.svg"
          alt="Site Logo"
          height={60}
          width={60}
          style:width="auto"
        />
      </a>
      <button
        class="navbar-toggler"
        type="button"
        data-bs-toggle="collapse"
        data-bs-target="#navbarNav"
        aria-controls="navbarNav"
        aria-expanded="false"
        aria-label="Toggle navigation"
      >
        <span class="navbar-toggler-icon"></span>
      </button>
      <div class="collapse navbar-collapse" id="navbarNav">
        <ul class="navbar-nav mx-auto">
          <!-- Commune Dropdown -->
          <li class="nav-item dropdown {t.navGap} text-nowrap">
            <!-- <a
            class="nav-link dropdown-toggle"
            href={toLocaleHref("/reactor")}
            id="communeDropdown"
            role="button"
            data-bs-toggle="dropdown"
            aria-expanded="false"
          >
            {t.commune}
          </a>
          <ul class="dropdown-menu" aria-labelledby="communeDropdown">
            <li><a class="dropdown-item" href={toLocaleHref("/the-law")}>{t.theLaw}</a></li>
            <li><a class="dropdown-item" href={toLocaleHref("/rules")}>{t.rules}</a></li>
            <li><a class="dropdown-item" href={toLocaleHref("/new-english")}>{t.newEnglish}</a></li>
            <li><a class="dropdown-item" href={toLocaleHref("/new-calendar")}>{t.newCalendar}</a></li>
            <li><a class="dropdown-item" href={toLocaleHref("/news")}>{t.news}</a></li>
            <li><a class="dropdown-item" href={toLocaleHref("/users")}>{t.users}</a></li>
            <li><a class="dropdown-item" href={toLocaleHref("/communes")}>{t.communes}</a></li>
            <li><a class="dropdown-item" href={toLocaleHref("/profile")}>{t.profile}</a></li>
          </ul> -->

            <a href={toLocaleHref("/")} class="nav-link">{t.commune}</a>
          </li>

          <!-- Reactor Links -->
          <li class={`nav-item ${t.navGap} text-nowrap`}>
            <a href={toLocaleHref("/reactor")} class="nav-link">{t.feed}</a>
          </li>
          <!-- <li class={`nav-item ${t.navGap} text-nowrap`}>
            <a href={toLocaleHref("/reactor/hole")} class="nav-link">{t.hole}</a>
          </li> -->
          <li class={`nav-item ${t.navGap} text-nowrap`}>
            <a href={toLocaleHref("/reactor/hubs")} class="nav-link">{t.hubs}</a>
          </li>
          <li class={`nav-item ${t.navGap} text-nowrap`}>
            <a href={toLocaleHref("/reactor/communities")} class="nav-link">{t.communities}</a>
          </li>
        </ul>
        <ul class="navbar-nav">
          <!-- <li class="nav-item">
          <a href={toLocaleHref("/profile")} class="btn btn-primary btn-sm">
            {t.profile}
          </a>
        </li> -->
          <li class="nav-item">
            <LocaleSwitcher currentLocale={routeLocale} />
          </li>
        </ul>
      </div>
    </div>
  </nav>

  <main class="container-fluid flex-grow-1 mb-5 reactor-container">
    {@render children()}
  </main>
</div>

<style>
  .page-wrapper {
    min-height: 100vh;
    display: flex;
    flex-direction: column;
  }

  .navbar {
    padding: 0;
  }

  .reactor-navbar {
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    z-index: 1030;
    backdrop-filter: blur(5px);
    background-color: rgba(28, 29, 29, 0.95) !important;
  }

  .reactor-navbar .nav-link {
    font-size: 1.05rem;
    padding: 0.7rem 1rem !important;
    transition: color 0.2s ease;
    color: #e9ecef; /* Light color for dark background */
    font-weight: 500;
    display: block;
  }

  .reactor-navbar .nav-link:hover {
    color: #0d6efd;
  }

  .reactor-navbar .nav-link.active {
    color: #0d6efd;
  }

  .reactor-navbar .navbar-brand {
    display: flex;
    align-items: center;
  }

  .reactor-navbar .navbar-collapse {
    justify-content: space-between;
  }

  .reactor-navbar .navbar-nav.mx-auto {
    display: flex;
    justify-content: center;
  }

  .reactor-navbar .nav-item {
    display: flex;
    align-items: center;
  }

  .reactor-navbar .dropdown-menu {
    background-color: rgba(33, 37, 41, 0.95);
    border: 1px solid rgba(255, 255, 255, 0.1);
  }

  .reactor-navbar .dropdown-item {
    color: #e9ecef;
  }

  .reactor-navbar .dropdown-item:hover {
    background-color: rgba(13, 110, 253, 0.1);
    color: #0d6efd;
  }

  .reactor-container {
    max-width: 1400px;
    margin: 0 auto;
  }

  @media (max-width: 991.98px) {
    .reactor-navbar .navbar-collapse {
      padding: 0.5rem 0;
      flex-direction: column;
    }

    .reactor-navbar .navbar-nav {
      text-align: center;
      margin-bottom: 0.5rem;
    }

    .reactor-navbar .nav-item {
      margin: 0.2rem 0 !important;
    }

    .reactor-navbar .navbar-nav.mx-auto {
      margin-left: 0 !important;
      margin-right: 0 !important;
    }
  }
</style>
