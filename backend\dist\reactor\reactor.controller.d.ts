import { CurrentUser } from "src/auth/types";
import { ReactorHubService } from "./reactor-hub.service";
import { ReactorPostService } from "./reactor-post.service";
import { ReactorLensService } from "./lens/reactor-lens.service";
import { ReactorCommentService } from "./reactor-comment.service";
import { ReactorCommunityService } from "./reactor-community.service";
export declare class ReactorController {
    private readonly reactorPostService;
    private readonly reactorCommentService;
    private readonly reactorLensService;
    private readonly reactorHubService;
    private readonly reactorCommunityService;
    constructor(reactorPostService: ReactorPostService, reactorCommentService: ReactorCommentService, reactorLensService: ReactorLensService, reactorHubService: ReactorHubService, reactorCommunityService: ReactorCommunityService);
    createMockPost(user: CurrentUser): Promise<{
        id: string;
        hubId: string | null;
        createdAt: Date;
        updatedAt: Date;
        deletedAt: Date | null;
        authorId: string;
        communityId: string | null;
        isAnonymous: boolean;
        anonimityReason: string | null;
        deleteReason: string | null;
    }>;
    updateHubImage(id: string, user: CurrentUser, file: Express.Multer.File): Promise<void>;
    deleteHubImage(id: string, user: CurrentUser): Promise<void>;
    updateCommunityImage(id: string, user: CurrentUser, file: Express.Multer.File): Promise<void>;
    deleteCommunityImage(id: string, user: CurrentUser): Promise<void>;
}
