<script lang="ts">
  import type { Common } from "@commune/api";

  import { onMount } from "svelte";
  import { Consts } from "@commune/api";
  import { goto } from "$app/navigation";
  import { getClient } from "$lib/acrpc";
  import { Modal, TagPicker } from "$components";
  import PostCard from "./post-card.svelte";
  import RightMenu from "./right-menu.svelte";
  import LocalizedInput from "../(index)/components/localized-input.svelte";
  import LocalizedTextarea from "../(index)/components/localized-textarea.svelte";

  // Create Post i18n
  const i18n = {
    en: {
      _page: {
        title: "Feed — Reactor",
      },
      createPost: "Create Post",
      createPostTitle: "Create New Post",
      cancel: "Cancel",
      create: "Create",
      hubId: "Hub ID",
      hubIdPlaceholder: "Enter hub ID (optional)...",
      communityId: "Community ID",
      communityIdPlaceholder: "Enter community ID (optional)...",
      hubDisabledByCommunity: "Hub ID is disabled when Community ID is specified",
      communityDisabledByHub: "Community ID is disabled when Hub ID is specified",
      tags: "Tags",
      title: "Title",
      titlePlaceholder: "Enter post title...",
      body: "Body",
      bodyPlaceholder: "Write your post content...",
      titleRequired: "Title is required",
      bodyRequired: "Body is required",
      createSuccess: "Post created successfully!",
      createError: "Failed to create post",
      noPosts: "No posts found",
      loadingMore: "Loading more posts...",
    },
    ru: {
      _page: {
        title: "Лента — Реактор",
      },
      createPost: "Создать пост",
      createPostTitle: "Создать новый пост",
      cancel: "Отмена",
      create: "Создать",
      hubId: "ID хаба",
      hubIdPlaceholder: "Введите ID хаба (необязательно)...",
      communityId: "ID сообщества",
      communityIdPlaceholder: "Введите ID сообщества (необязательно)...",
      hubDisabledByCommunity: "ID хаба отключен, когда указан ID сообщества",
      communityDisabledByHub: "ID сообщества отключен, когда указан ID хаба",
      tags: "Теги",
      title: "Заголовок",
      titlePlaceholder: "Введите заголовок поста...",
      body: "Содержание",
      bodyPlaceholder: "Напишите содержание поста...",
      titleRequired: "Заголовок обязателен",
      bodyRequired: "Содержание обязательно",
      createSuccess: "Пост успешно создан!",
      createError: "Не удалось создать пост",
      noPosts: "Посты не найдены",
      loadingMore: "Загружаем больше постов...",
    },
  };

  const { fetcher: api } = getClient();

  const { data } = $props();
  const { locale, toLocaleHref, getAppropriateLocalization } = $derived(data);

  const t = $derived(i18n[locale]);

  // State using runes - initialize with SSR data
  let posts = $state(data.posts);
  let currentPage = $state(1);
  let isHasMorePosts = $state(data.isHasMorePosts);
  let isLoadingMore = $state(false);
  let error = $state<string | null>(null);

  let isRightMenuExpanded = $state(false);

  let showCreatePostModal = $state(false);

  // Reference to the sentinel element for intersection observer
  let sentinelElement = $state<HTMLElement | null>(null);

  // Form state
  let postTitle = $state<Common.Localizations>([]);
  let postBody = $state<Common.Localizations>([]);
  let hubId = $state<string>("");
  let communityId = $state<string>("");
  let selectedTags = $state<string[]>([]);
  let isSubmitting = $state(false);
  let formError = $state<string | null>(null);
  let formSuccess = $state<string | null>(null);

  function openCreatePostModal() {
    // Reset form state
    postTitle = [];
    postBody = [];
    hubId = "";
    communityId = "";
    selectedTags = [];
    formError = null;
    formSuccess = null;
    isSubmitting = false;
    showCreatePostModal = true;
  }

  function closeCreatePostModal() {
    showCreatePostModal = false;
  }

  async function handleCreatePost() {
    // Clear previous messages
    formError = null;
    formSuccess = null;

    // Validate form
    const hasTitle = postTitle.some((item) => item.value.trim().length > 0);
    const hasBody = postBody.some((item) => item.value.trim().length > 0);

    if (!hasTitle) {
      formError = t.titleRequired;
      return;
    }

    if (!hasBody) {
      formError = t.bodyRequired;
      return;
    }

    isSubmitting = true;

    try {
      const { id } = await api.reactor.post.post({
        hubId: hubId || null,
        communityId: communityId || null,
        title: postTitle.filter((item) => item.value.trim().length > 0),
        body: postBody.filter((item) => item.value.trim().length > 0),
        tagIds: selectedTags,
      });

      formSuccess = t.createSuccess;

      setTimeout(() => {
        goto(toLocaleHref(`/reactor/${id}`));
      }, 1500);
    } catch (error) {
      console.error("Error creating post:", error);
      formError = error instanceof Error ? error.message : t.createError;
    } finally {
      isSubmitting = false;
    }
  }

  async function loadMorePosts() {
    if (isLoadingMore || !isHasMorePosts) return;

    isLoadingMore = true;
    error = null;

    try {
      const nextPage = currentPage + 1;

      const newPosts = await api.reactor.post.list.get({
        pagination: { page: nextPage },
        lensId: null,
      });

      posts = [...posts, ...newPosts];
      currentPage = nextPage;

      isHasMorePosts = newPosts.length === Consts.PAGE_SIZE;
    } catch (err) {
      error = err instanceof Error ? err.message : "An error occurred while fetching posts";
      console.error(err);
    } finally {
      isLoadingMore = false;
    }
  }

  // Setup intersection observer for infinite scroll
  onMount(() => {
    let observer: IntersectionObserver;

    const setupObserver = () => {
      if (!sentinelElement) return;

      observer = new IntersectionObserver(
        (entries) => {
          const entry = entries[0];
          if (entry.isIntersecting && isHasMorePosts && !isLoadingMore) {
            loadMorePosts();
          }
        },
        {
          rootMargin: "100px", // Start loading 100px before the sentinel comes into view
          threshold: 0.1,
        },
      );

      observer.observe(sentinelElement);
    };

    // Try to setup observer immediately, or wait for element to be available
    if (sentinelElement) {
      setupObserver();
    } else {
      // Use a small delay to allow the DOM to render
      setTimeout(setupObserver, 100);
    }

    // Cleanup observer on component destroy
    return () => {
      if (observer) {
        observer.disconnect();
      }
    };
  });
</script>

<svelte:head>
  <title>{t._page.title}</title>
</svelte:head>

<div class="row g-4 mt-3">
  <div class="col-1"></div>

  <!-- Left Menu (2-3 columns) -->
  <div class="col-2">
    <!-- <LeftMenu {locale} /> -->
  </div>

  <!-- Feed (4-9 columns) -->
  <div class="col-6">
    <div class="feed">
      {#if posts.length === 0}
        <div class="text-center py-5">
          <p class="text-muted">{t.noPosts}</p>
        </div>
      {:else}
        {#each posts as post (post.id)}
          <PostCard {post} {locale} {toLocaleHref} {getAppropriateLocalization} />
        {/each}
      {/if}

      <!-- Infinite scroll sentinel element -->
      {#if isHasMorePosts}
        <div bind:this={sentinelElement} class="text-center py-3">
          {#if isLoadingMore}
            <div class="spinner-border spinner-border-sm" role="status">
              <span class="visually-hidden">{t.loadingMore}</span>
            </div>
            <p class="text-muted mt-2 mb-0">{t.loadingMore}</p>
          {/if}
        </div>
      {/if}

      {#if error}
        <div class="alert alert-danger" role="alert">
          {error}
        </div>
      {/if}
    </div>
  </div>

  <!-- Right Menu (10-11 columns) -->
  <div class="col-2">
    <!-- Create Post Button -->
    <div
      class="mb-3 create-post-btn-container {isRightMenuExpanded ? 'with-right-menu-expanded' : ''}"
    >
      <button
        class="btn btn-outline-secondary w-100 create-post-btn"
        onclick={openCreatePostModal}
        aria-label={t.createPost}
      >
        <i class="bi bi-plus-circle me-2"></i>
        {t.createPost}
      </button>
    </div>

    <RightMenu {locale} {toLocaleHref} bind:isExpanded={isRightMenuExpanded} />
  </div>
</div>

<!-- Create Post Modal -->
<Modal
  show={showCreatePostModal}
  title={t.createPostTitle}
  onClose={closeCreatePostModal}
  onSubmit={handleCreatePost}
  submitText={t.create}
  cancelText={t.cancel}
  submitDisabled={isSubmitting ||
    !postTitle.some((item) => item.value.trim().length > 0) ||
    !postBody.some((item) => item.value.trim().length > 0)}
  {isSubmitting}
>
  <form>
    <!-- Hub ID Input -->
    <div class="mb-3">
      <label for="post-hub-id" class="form-label">{t.hubId}</label>
      <input
        type="text"
        class="form-control"
        id="post-hub-id"
        placeholder={t.hubIdPlaceholder}
        bind:value={hubId}
        disabled={communityId.trim().length > 0}
      />
      {#if communityId.trim().length > 0}
        <div class="form-text text-muted">{t.hubDisabledByCommunity}</div>
      {/if}
    </div>

    <!-- Community ID Input -->
    <div class="mb-3">
      <label for="post-community-id" class="form-label">{t.communityId}</label>
      <input
        type="text"
        class="form-control"
        id="post-community-id"
        placeholder={t.communityIdPlaceholder}
        bind:value={communityId}
        disabled={hubId.trim().length > 0}
      />
      {#if hubId.trim().length > 0}
        <div class="form-text text-muted">{t.communityDisabledByHub}</div>
      {/if}
    </div>

    <!-- Title Input -->
    <LocalizedInput
      {locale}
      id="post-title"
      label={t.title}
      placeholder={t.titlePlaceholder}
      required
      bind:value={postTitle}
    />

    <!-- Body Textarea -->
    <LocalizedTextarea
      {locale}
      id="post-body"
      label={t.body}
      placeholder={t.bodyPlaceholder}
      rows={6}
      required
      bind:value={postBody}
    />

    <!-- Tag Picker -->
    <TagPicker {locale} label={t.tags} bind:selectedTagIds={selectedTags} />

    <!-- Error Message -->
    {#if formError}
      <div class="alert alert-danger mt-3" role="alert">
        {formError}
      </div>
    {/if}

    <!-- Success Message -->
    {#if formSuccess}
      <div class="alert alert-success mt-3" role="alert">
        {formSuccess}
      </div>
    {/if}
  </form>
</Modal>

<style lang="scss">
  .feed {
    max-width: 100%;
  }

  .create-post-btn-container {
    opacity: 0.5;

    &:hover,
    &.with-right-menu-expanded {
      opacity: 1;
    }
  }

  @media (max-width: 767.98px) {
    .feed {
      margin-top: 1rem;
    }
  }

  .create-post-btn {
    transition: all 0.2s ease;
  }

  .create-post-btn:hover {
    border-color: var(--bs-success);
    color: var(--bs-success);
    background-color: transparent;
  }
</style>
