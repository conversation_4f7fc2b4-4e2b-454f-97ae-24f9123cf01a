"use strict";Object.defineProperty(exports, "__esModule", {value: true}); function _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; } function _nullishCoalesce(lhs, rhsFn) { if (lhs != null) { return lhs; } else { return rhsFn(); } } function _optionalChain(ops) { let lastAccessLHS = undefined; let value = ops[0]; let i = 1; while (i < ops.length) { const op = ops[i]; const fn = ops[i + 1]; i += 2; if ((op === 'optionalAccess' || op === 'optionalCall') && value == null) { return undefined; } if (op === 'access' || op === 'optionalAccess') { lastAccessLHS = value; value = fn(value); } else if (op === 'call' || op === 'optionalCall') { value = fn((...args) => value.call(lastAccessLHS, ...args)); lastAccessLHS = undefined; } } return value; }




var _chunk467PIPJEcjs = require('../chunk-467PIPJE.cjs');
require('../chunk-Q7SFCCGT.cjs');

// src/acrpc/server.ts
var _zod = require('zod');
var _express = require('express'); var _express2 = _interopRequireDefault(_express);
function isEndpoint(schemaEntry) {
  return schemaEntry != null && typeof schemaEntry === "object" && ("input" in schemaEntry && (schemaEntry["input"] instanceof _zod.z.ZodType || schemaEntry["input"] === null || schemaEntry["input"] === void 0) && ("output" in schemaEntry && (schemaEntry["output"] instanceof _zod.z.ZodType || schemaEntry["output"] === null || schemaEntry["output"] === void 0)));
}
function getInput(req, inputSchema, transformer) {
  let body = null;
  _chunk467PIPJEcjs.dir.call(void 0, {
    method: req.method,
    query: req.query,
    body: req.body
  });
  if (req.method === "GET") {
    body = req.query.__body ? decodeURIComponent(req.query.__body) : null;
    _chunk467PIPJEcjs.log.call(void 0, "get", { body });
    if (inputSchema && !body) {
      return {
        success: false,
        status: 400,
        response: {
          error: "No __body provided"
        }
      };
    }
  } else {
    body = req.body;
    _chunk467PIPJEcjs.log.call(void 0, "non-get", { body });
    if (inputSchema && !body) {
      return {
        success: false,
        status: 400,
        response: {
          error: "No body provided"
        }
      };
    }
  }
  _chunk467PIPJEcjs.log.call(void 0, "before deserialize", { body });
  const rawInput = body ? transformer.deserialize(body) : null;
  _chunk467PIPJEcjs.dir.call(void 0, { rawInput });
  if (inputSchema) {
    const schemaParseResult = inputSchema.safeParse(rawInput);
    _chunk467PIPJEcjs.dir.call(void 0, { schemaParseResult });
    if (schemaParseResult.success) {
      return {
        success: true,
        data: schemaParseResult.data
      };
    }
    return {
      success: false,
      status: 400,
      response: schemaParseResult.error.issues.map((issue) => ({
        path: issue.path,
        message: issue.message
      }))
    };
  }
  return {
    success: true,
    data: rawInput
  };
}
function createServer(schema, handlers, options) {
  const transformer = _nullishCoalesce(_optionalChain([options, 'optionalAccess', _ => _.transformer]), () => ( _chunk467PIPJEcjs.jsonTransformer));
  const getMetadata = _optionalChain([options, 'optionalAccess', _2 => _2.getMetadata]);
  const router = _express.Router.call(void 0, );
  const textMiddleware = _express2.default.text({ type: "*/*" });
  function fillRouter(schema2, handlers2, names) {
    _chunk467PIPJEcjs.log.call(void 0, { names });
    for (const [name, handler] of Object.entries(handlers2)) {
      _chunk467PIPJEcjs.log.call(void 0, { name });
      const schemaEntry = schema2[name];
      if (isEndpoint(schemaEntry)) {
        const path = ["", ...names].join("/");
        const method = name;
        const okStatus = method === "post" ? 201 : 200;
        _chunk467PIPJEcjs.log.call(void 0, `Registering ${method.toUpperCase()} ${path}...`);
        if (method !== "get") {
          router[method](path, textMiddleware);
        }
        router[method](
          path,
          async (req, res) => {
            let metadata = void 0;
            const isMetadataUsed = _nullishCoalesce(schemaEntry.isMetadataUsed, () => ( true));
            const isMetadataRequired = _nullishCoalesce(schemaEntry.isMetadataRequired, () => ( true));
            _chunk467PIPJEcjs.dir.call(void 0, {
              getMetadata,
              isMetadataUsed,
              isMetadataRequired
            });
            if (isMetadataUsed) {
              metadata = _nullishCoalesce(_optionalChain([getMetadata, 'optionalCall', _3 => _3(req)]), () => ( null));
              _chunk467PIPJEcjs.dir.call(void 0, { metadata });
              if (isMetadataRequired && !metadata) {
                return res.status(400).json({
                  error: "Metadata cannot be parsed."
                });
              }
            }
            const inputParseResult = getInput(
              req,
              schemaEntry.input,
              transformer
            );
            _chunk467PIPJEcjs.dir.call(void 0, { inputParseResult });
            if (!inputParseResult.success) {
              return res.status(inputParseResult.status).json(inputParseResult.response);
            }
            _chunk467PIPJEcjs.dir.call(void 0, { handlers: handlers2 });
            const rawOutput = await handler(
              inputParseResult.data,
              _nullishCoalesce(metadata, () => ( null)),
              {
                req,
                res
              }
            );
            let output = null;
            if (schemaEntry.output !== null) {
              let parsedOutput = null;
              if (schemaEntry.output) {
                const outputParseResult = schemaEntry.output.safeParse(rawOutput);
                _chunk467PIPJEcjs.dir.call(void 0, {
                  rawOutput,
                  outputParseResult
                });
                if (outputParseResult.error) {
                  return res.status(500).json(outputParseResult.error);
                }
                parsedOutput = outputParseResult.data;
              } else {
                parsedOutput = rawOutput;
              }
              const serializedOutput = transformer.serialize(parsedOutput);
              _chunk467PIPJEcjs.dir.call(void 0, { serializedOutput });
              output = serializedOutput;
            }
            if (schemaEntry.cacheControl) {
              res.setHeader("Cache-Control", schemaEntry.cacheControl);
            }
            res.status(okStatus);
            if (output != null) {
              res.send(output);
            } else {
              res.send("");
            }
          }
        );
      } else {
        fillRouter(
          schema2[name],
          handlers2[name],
          [...names, _chunk467PIPJEcjs.kebabTransformer.transform(name)]
        );
      }
    }
  }
  fillRouter(schema, handlers, []);
  function register(handlers2) {
    fillRouter(schema, handlers2, []);
  }
  return {
    router,
    register
  };
}


exports.createServer = createServer;
//# sourceMappingURL=server.cjs.map