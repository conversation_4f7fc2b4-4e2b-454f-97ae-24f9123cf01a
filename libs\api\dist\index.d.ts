import { I as Infer } from './types.d-BnyDggQq.js';
export { t as Types } from './types.d-BnyDggQq.js';
import { z } from 'zod';

declare const PAGE_SIZE = 20;
declare const ALLOWED_IMAGE_FILE_TYPES: string[];
declare const MAX_IMAGE_FILE_SIZE: number;

declare const consts_ALLOWED_IMAGE_FILE_TYPES: typeof ALLOWED_IMAGE_FILE_TYPES;
declare const consts_MAX_IMAGE_FILE_SIZE: typeof MAX_IMAGE_FILE_SIZE;
declare const consts_PAGE_SIZE: typeof PAGE_SIZE;
declare namespace consts {
  export { consts_ALLOWED_IMAGE_FILE_TYPES as ALLOWED_IMAGE_FILE_TYPES, consts_MAX_IMAGE_FILE_SIZE as MAX_IMAGE_FILE_SIZE, consts_PAGE_SIZE as PAGE_SIZE };
}

declare const id: z.ZodString;
declare const idOrNull: z.ZodDefault<z.ZodNullable<z.ZodString>>;
declare const url: z.ZodString;
declare const email: z.ZodString;
declare const query: z.ZodString;
declare const imageUrl: z.ZodString;
declare const maybeImageUrl: z.ZodNullable<z.ZodString>;
declare const createdAt: z.ZodDate;
declare const updatedAt: z.ZodDate;
declare const deletedAt: z.ZodNullable<z.ZodDate>;
declare const searchIds: z.ZodArray<z.ZodString, "many">;
declare const searchQuery: z.ZodString;
declare const stringToDate: z.ZodPipeline<z.ZodUnion<[z.ZodNumber, z.ZodString, z.ZodDate]>, z.ZodDate>;
declare function JsonStringToObject<T extends z.ZodRawShape>(schema: T): z.ZodPipeline<z.ZodEffects<z.ZodString, any, string>, z.ZodObject<T, "strip", z.ZodTypeAny, z.objectUtil.addQuestionMarks<z.baseObjectOutputType<T>, any> extends infer T_1 ? { [k in keyof T_1]: z.objectUtil.addQuestionMarks<z.baseObjectOutputType<T>, any>[k]; } : never, z.baseObjectInputType<T> extends infer T_2 ? { [k_1 in keyof T_2]: z.baseObjectInputType<T>[k_1]; } : never>>;
declare function FormDataToObject<T extends z.ZodRawShape>(schema: T): z.ZodObject<{
    data: z.ZodPipeline<z.ZodEffects<z.ZodString, any, string>, z.ZodObject<T, "strip", z.ZodTypeAny, z.objectUtil.addQuestionMarks<z.baseObjectOutputType<T>, any> extends infer T_1 ? { [k in keyof T_1]: z.objectUtil.addQuestionMarks<z.baseObjectOutputType<T>, any>[k]; } : never, z.baseObjectInputType<T> extends infer T_2 ? { [k_1 in keyof T_2]: z.baseObjectInputType<T>[k_1]; } : never>>;
}, "strip", z.ZodTypeAny, z.objectUtil.addQuestionMarks<z.baseObjectOutputType<{
    data: z.ZodPipeline<z.ZodEffects<z.ZodString, any, string>, z.ZodObject<T, "strip", z.ZodTypeAny, z.objectUtil.addQuestionMarks<z.baseObjectOutputType<T>, any> extends infer T_6 ? { [k in keyof T_6]: z.objectUtil.addQuestionMarks<z.baseObjectOutputType<T>, any>[k]; } : never, z.baseObjectInputType<T> extends infer T_7 ? { [k_1 in keyof T_7]: z.baseObjectInputType<T>[k_1]; } : never>>;
}>, any> extends infer T_3 ? { [k_2 in keyof T_3]: z.objectUtil.addQuestionMarks<z.baseObjectOutputType<{
    data: z.ZodPipeline<z.ZodEffects<z.ZodString, any, string>, z.ZodObject<T, "strip", z.ZodTypeAny, z.objectUtil.addQuestionMarks<z.baseObjectOutputType<T>, any> extends infer T_4 ? { [k in keyof T_4]: z.objectUtil.addQuestionMarks<z.baseObjectOutputType<T>, any>[k]; } : never, z.baseObjectInputType<T> extends infer T_5 ? { [k_1 in keyof T_5]: z.baseObjectInputType<T>[k_1]; } : never>>;
}>, any>[k_2]; } : never, {
    data: string;
}>;
type ObjectWithId = Infer<typeof ObjectWithIdSchema>;
declare const ObjectWithIdSchema: z.ZodObject<{
    id: z.ZodString;
}, "strip", z.ZodTypeAny, {
    id: string;
}, {
    id: string;
}>;
type WebsiteLocale = Infer<typeof WebsiteLocaleSchema>;
declare const WebsiteLocaleSchema: z.ZodEnum<["en", "ru"]>;
type LocalizationLocale = Infer<typeof LocalizationLocaleSchema>;
declare const LocalizationLocaleSchema: z.ZodEnum<["en", "ru"]>;
type LocalizationLocales = Infer<typeof LocalizationLocalesSchema>;
declare const LocalizationLocalesSchema: z.ZodArray<z.ZodEnum<["en", "ru"]>, "many">;
type Localization = Infer<typeof LocalizationSchema>;
declare const LocalizationSchema: z.ZodObject<{
    locale: z.ZodEnum<["en", "ru"]>;
    value: z.ZodString;
}, "strip", z.ZodTypeAny, {
    value: string;
    locale: "en" | "ru";
}, {
    value: string;
    locale: "en" | "ru";
}>;
type Localizations = Infer<typeof LocalizationsSchema>;
declare const LocalizationsSchema: z.ZodArray<z.ZodObject<{
    locale: z.ZodEnum<["en", "ru"]>;
    value: z.ZodString;
}, "strip", z.ZodTypeAny, {
    value: string;
    locale: "en" | "ru";
}, {
    value: string;
    locale: "en" | "ru";
}>, "many">;
type Image = Infer<typeof ImageSchema>;
declare const ImageSchema: z.ZodObject<{
    id: z.ZodString;
    url: z.ZodString;
    createdAt: z.ZodPipeline<z.ZodUnion<[z.ZodNumber, z.ZodString, z.ZodDate]>, z.ZodDate>;
    updatedAt: z.ZodPipeline<z.ZodUnion<[z.ZodNumber, z.ZodString, z.ZodDate]>, z.ZodDate>;
}, "strip", z.ZodTypeAny, {
    id: string;
    url: string;
    createdAt: Date;
    updatedAt: Date;
}, {
    id: string;
    url: string;
    createdAt: string | number | Date;
    updatedAt: string | number | Date;
}>;
type Images = Infer<typeof ImagesSchema>;
declare const ImagesSchema: z.ZodArray<z.ZodObject<{
    id: z.ZodString;
    url: z.ZodString;
    createdAt: z.ZodPipeline<z.ZodUnion<[z.ZodNumber, z.ZodString, z.ZodDate]>, z.ZodDate>;
    updatedAt: z.ZodPipeline<z.ZodUnion<[z.ZodNumber, z.ZodString, z.ZodDate]>, z.ZodDate>;
}, "strip", z.ZodTypeAny, {
    id: string;
    url: string;
    createdAt: Date;
    updatedAt: Date;
}, {
    id: string;
    url: string;
    createdAt: string | number | Date;
    updatedAt: string | number | Date;
}>, "many">;
declare const pagination: {
    offset: z.ZodDefault<z.ZodNumber>;
    limit: z.ZodDefault<z.ZodNumber>;
    page: z.ZodDefault<z.ZodNumber>;
    size: z.ZodDefault<z.ZodNumber>;
};
type Pagination = Infer<typeof PaginationSchema>;
declare const PaginationSchema: z.ZodDefault<z.ZodObject<{
    page: z.ZodDefault<z.ZodNumber>;
    size: z.ZodDefault<z.ZodNumber>;
}, "strip", z.ZodTypeAny, {
    page: number;
    size: number;
}, {
    page?: number | undefined;
    size?: number | undefined;
}>>;
declare function parseInput<T extends z.ZodTypeAny>(schema: T, value: z.input<T>): z.output<T>;
declare function parseUnknown<T extends z.ZodTypeAny>(schema: T, value: unknown): z.output<T>;

declare const common_FormDataToObject: typeof FormDataToObject;
type common_Image = Image;
declare const common_ImageSchema: typeof ImageSchema;
type common_Images = Images;
declare const common_ImagesSchema: typeof ImagesSchema;
declare const common_JsonStringToObject: typeof JsonStringToObject;
type common_Localization = Localization;
type common_LocalizationLocale = LocalizationLocale;
declare const common_LocalizationLocaleSchema: typeof LocalizationLocaleSchema;
type common_LocalizationLocales = LocalizationLocales;
declare const common_LocalizationLocalesSchema: typeof LocalizationLocalesSchema;
declare const common_LocalizationSchema: typeof LocalizationSchema;
type common_Localizations = Localizations;
declare const common_LocalizationsSchema: typeof LocalizationsSchema;
type common_ObjectWithId = ObjectWithId;
declare const common_ObjectWithIdSchema: typeof ObjectWithIdSchema;
type common_Pagination = Pagination;
declare const common_PaginationSchema: typeof PaginationSchema;
type common_WebsiteLocale = WebsiteLocale;
declare const common_WebsiteLocaleSchema: typeof WebsiteLocaleSchema;
declare const common_createdAt: typeof createdAt;
declare const common_deletedAt: typeof deletedAt;
declare const common_email: typeof email;
declare const common_id: typeof id;
declare const common_idOrNull: typeof idOrNull;
declare const common_imageUrl: typeof imageUrl;
declare const common_maybeImageUrl: typeof maybeImageUrl;
declare const common_pagination: typeof pagination;
declare const common_parseInput: typeof parseInput;
declare const common_parseUnknown: typeof parseUnknown;
declare const common_query: typeof query;
declare const common_searchIds: typeof searchIds;
declare const common_searchQuery: typeof searchQuery;
declare const common_stringToDate: typeof stringToDate;
declare const common_updatedAt: typeof updatedAt;
declare const common_url: typeof url;
declare namespace common {
  export { common_FormDataToObject as FormDataToObject, type common_Image as Image, common_ImageSchema as ImageSchema, type common_Images as Images, common_ImagesSchema as ImagesSchema, common_JsonStringToObject as JsonStringToObject, type common_Localization as Localization, type common_LocalizationLocale as LocalizationLocale, common_LocalizationLocaleSchema as LocalizationLocaleSchema, type common_LocalizationLocales as LocalizationLocales, common_LocalizationLocalesSchema as LocalizationLocalesSchema, common_LocalizationSchema as LocalizationSchema, type common_Localizations as Localizations, common_LocalizationsSchema as LocalizationsSchema, type common_ObjectWithId as ObjectWithId, common_ObjectWithIdSchema as ObjectWithIdSchema, type common_Pagination as Pagination, common_PaginationSchema as PaginationSchema, type common_WebsiteLocale as WebsiteLocale, common_WebsiteLocaleSchema as WebsiteLocaleSchema, common_createdAt as createdAt, common_deletedAt as deletedAt, common_email as email, common_id as id, common_idOrNull as idOrNull, common_imageUrl as imageUrl, common_maybeImageUrl as maybeImageUrl, common_pagination as pagination, common_parseInput as parseInput, common_parseUnknown as parseUnknown, common_query as query, common_searchIds as searchIds, common_searchQuery as searchQuery, common_stringToDate as stringToDate, common_updatedAt as updatedAt, common_url as url };
}

declare const otp: z.ZodString;
type SendOtpInput = Infer<typeof SendOtpInputSchema>;
declare const SendOtpInputSchema: z.ZodObject<{
    email: z.ZodString;
}, "strip", z.ZodTypeAny, {
    email: string;
}, {
    email: string;
}>;
type SendOtpOutput = Infer<typeof SendOtpOutputSchema>;
declare const SendOtpOutputSchema: z.ZodObject<{
    isSent: z.ZodBoolean;
}, "strip", z.ZodTypeAny, {
    isSent: boolean;
}, {
    isSent: boolean;
}>;
type SignupInput = Infer<typeof SignupInputSchema>;
declare const SignupInputSchema: z.ZodObject<{
    referrerId: z.ZodNullable<z.ZodString>;
    email: z.ZodString;
    otp: z.ZodString;
}, "strip", z.ZodTypeAny, {
    email: string;
    referrerId: string | null;
    otp: string;
}, {
    email: string;
    referrerId: string | null;
    otp: string;
}>;
type SigninInput = Infer<typeof SigninInputSchema>;
declare const SigninInputSchema: z.ZodObject<{
    email: z.ZodString;
    otp: z.ZodString;
}, "strip", z.ZodTypeAny, {
    email: string;
    otp: string;
}, {
    email: string;
    otp: string;
}>;
type SuccessfulOutput = Infer<typeof SuccessfulOutputSchema>;
declare const SuccessfulOutputSchema: z.ZodObject<{
    id: z.ZodString;
    email: z.ZodString;
    role: z.ZodEnum<["admin", "moderator", "user"]>;
}, "strip", z.ZodTypeAny, {
    id: string;
    email: string;
    role: "admin" | "moderator" | "user";
}, {
    id: string;
    email: string;
    role: "admin" | "moderator" | "user";
}>;

type auth_SendOtpInput = SendOtpInput;
declare const auth_SendOtpInputSchema: typeof SendOtpInputSchema;
type auth_SendOtpOutput = SendOtpOutput;
declare const auth_SendOtpOutputSchema: typeof SendOtpOutputSchema;
type auth_SigninInput = SigninInput;
declare const auth_SigninInputSchema: typeof SigninInputSchema;
type auth_SignupInput = SignupInput;
declare const auth_SignupInputSchema: typeof SignupInputSchema;
type auth_SuccessfulOutput = SuccessfulOutput;
declare const auth_SuccessfulOutputSchema: typeof SuccessfulOutputSchema;
declare const auth_otp: typeof otp;
declare namespace auth {
  export { type auth_SendOtpInput as SendOtpInput, auth_SendOtpInputSchema as SendOtpInputSchema, type auth_SendOtpOutput as SendOtpOutput, auth_SendOtpOutputSchema as SendOtpOutputSchema, type auth_SigninInput as SigninInput, auth_SigninInputSchema as SigninInputSchema, type auth_SignupInput as SignupInput, auth_SignupInputSchema as SignupInputSchema, type auth_SuccessfulOutput as SuccessfulOutput, auth_SuccessfulOutputSchema as SuccessfulOutputSchema, auth_otp as otp };
}

type CommuneMemberType = Infer<typeof CommuneMemberTypeSchema>;
declare const CommuneMemberTypeSchema: z.ZodEnum<["user"]>;
declare const communeName: z.ZodArray<z.ZodObject<{
    locale: z.ZodEnum<["en", "ru"]>;
    value: z.ZodString;
}, "strip", z.ZodTypeAny, {
    value: string;
    locale: "en" | "ru";
}, {
    value: string;
    locale: "en" | "ru";
}>, "many">;
declare const communeDescription: z.ZodArray<z.ZodObject<{
    locale: z.ZodEnum<["en", "ru"]>;
    value: z.ZodString;
}, "strip", z.ZodTypeAny, {
    value: string;
    locale: "en" | "ru";
}, {
    value: string;
    locale: "en" | "ru";
}>, "many">;
declare const communeMemberActorType: z.ZodEnum<["user"]>;
declare const communeMemberName: z.ZodUnion<[z.ZodArray<z.ZodObject<{
    locale: z.ZodEnum<["en", "ru"]>;
    value: z.ZodString;
}, "strip", z.ZodTypeAny, {
    value: string;
    locale: "en" | "ru";
}, {
    value: string;
    locale: "en" | "ru";
}>, "many">, z.ZodArray<z.ZodObject<{
    locale: z.ZodEnum<["en", "ru"]>;
    value: z.ZodString;
}, "strip", z.ZodTypeAny, {
    value: string;
    locale: "en" | "ru";
}, {
    value: string;
    locale: "en" | "ru";
}>, "many">]>;
type TransferHeadStatusInput = Infer<typeof TransferHeadStatusInputSchema>;
declare const TransferHeadStatusInputSchema: z.ZodObject<{
    communeId: z.ZodString;
    newHeadUserId: z.ZodString;
}, "strip", z.ZodTypeAny, {
    communeId: string;
    newHeadUserId: string;
}, {
    communeId: string;
    newHeadUserId: string;
}>;
type GetCommunesInput = Infer<typeof GetCommunesInputSchema>;
declare const GetCommunesInputSchema: z.ZodObject<{
    pagination: z.ZodOptional<z.ZodDefault<z.ZodObject<{
        page: z.ZodDefault<z.ZodNumber>;
        size: z.ZodDefault<z.ZodNumber>;
    }, "strip", z.ZodTypeAny, {
        page: number;
        size: number;
    }, {
        page?: number | undefined;
        size?: number | undefined;
    }>>>;
    ids: z.ZodOptional<z.ZodArray<z.ZodString, "many">>;
    query: z.ZodOptional<z.ZodString>;
    userId: z.ZodOptional<z.ZodString>;
}, "strip", z.ZodTypeAny, {
    pagination?: {
        page: number;
        size: number;
    } | undefined;
    ids?: string[] | undefined;
    query?: string | undefined;
    userId?: string | undefined;
}, {
    pagination?: {
        page?: number | undefined;
        size?: number | undefined;
    } | undefined;
    ids?: string[] | undefined;
    query?: string | undefined;
    userId?: string | undefined;
}>;
type GetCommuneOutput = Infer<typeof GetCommuneOutputSchema>;
declare const GetCommuneOutputSchema: z.ZodObject<{
    id: z.ZodString;
    name: z.ZodArray<z.ZodObject<{
        locale: z.ZodEnum<["en", "ru"]>;
        value: z.ZodString;
    }, "strip", z.ZodTypeAny, {
        value: string;
        locale: "en" | "ru";
    }, {
        value: string;
        locale: "en" | "ru";
    }>, "many">;
    description: z.ZodArray<z.ZodObject<{
        locale: z.ZodEnum<["en", "ru"]>;
        value: z.ZodString;
    }, "strip", z.ZodTypeAny, {
        value: string;
        locale: "en" | "ru";
    }, {
        value: string;
        locale: "en" | "ru";
    }>, "many">;
    headMember: z.ZodObject<{
        actorType: z.ZodEnum<["user"]>;
        actorId: z.ZodString;
        name: z.ZodUnion<[z.ZodArray<z.ZodObject<{
            locale: z.ZodEnum<["en", "ru"]>;
            value: z.ZodString;
        }, "strip", z.ZodTypeAny, {
            value: string;
            locale: "en" | "ru";
        }, {
            value: string;
            locale: "en" | "ru";
        }>, "many">, z.ZodArray<z.ZodObject<{
            locale: z.ZodEnum<["en", "ru"]>;
            value: z.ZodString;
        }, "strip", z.ZodTypeAny, {
            value: string;
            locale: "en" | "ru";
        }, {
            value: string;
            locale: "en" | "ru";
        }>, "many">]>;
        image: z.ZodNullable<z.ZodString>;
    }, "strip", z.ZodTypeAny, {
        name: {
            value: string;
            locale: "en" | "ru";
        }[];
        image: string | null;
        actorType: "user";
        actorId: string;
    }, {
        name: {
            value: string;
            locale: "en" | "ru";
        }[];
        image: string | null;
        actorType: "user";
        actorId: string;
    }>;
    memberCount: z.ZodNumber;
    image: z.ZodNullable<z.ZodString>;
    createdAt: z.ZodDate;
    updatedAt: z.ZodDate;
    deletedAt: z.ZodOptional<z.ZodNullable<z.ZodDate>>;
}, "strip", z.ZodTypeAny, {
    id: string;
    createdAt: Date;
    updatedAt: Date;
    name: {
        value: string;
        locale: "en" | "ru";
    }[];
    image: string | null;
    description: {
        value: string;
        locale: "en" | "ru";
    }[];
    headMember: {
        name: {
            value: string;
            locale: "en" | "ru";
        }[];
        image: string | null;
        actorType: "user";
        actorId: string;
    };
    memberCount: number;
    deletedAt?: Date | null | undefined;
}, {
    id: string;
    createdAt: Date;
    updatedAt: Date;
    name: {
        value: string;
        locale: "en" | "ru";
    }[];
    image: string | null;
    description: {
        value: string;
        locale: "en" | "ru";
    }[];
    headMember: {
        name: {
            value: string;
            locale: "en" | "ru";
        }[];
        image: string | null;
        actorType: "user";
        actorId: string;
    };
    memberCount: number;
    deletedAt?: Date | null | undefined;
}>;
type GetCommunesOutput = Infer<typeof GetCommunesOutputSchema>;
declare const GetCommunesOutputSchema: z.ZodArray<z.ZodObject<{
    id: z.ZodString;
    name: z.ZodArray<z.ZodObject<{
        locale: z.ZodEnum<["en", "ru"]>;
        value: z.ZodString;
    }, "strip", z.ZodTypeAny, {
        value: string;
        locale: "en" | "ru";
    }, {
        value: string;
        locale: "en" | "ru";
    }>, "many">;
    description: z.ZodArray<z.ZodObject<{
        locale: z.ZodEnum<["en", "ru"]>;
        value: z.ZodString;
    }, "strip", z.ZodTypeAny, {
        value: string;
        locale: "en" | "ru";
    }, {
        value: string;
        locale: "en" | "ru";
    }>, "many">;
    headMember: z.ZodObject<{
        actorType: z.ZodEnum<["user"]>;
        actorId: z.ZodString;
        name: z.ZodUnion<[z.ZodArray<z.ZodObject<{
            locale: z.ZodEnum<["en", "ru"]>;
            value: z.ZodString;
        }, "strip", z.ZodTypeAny, {
            value: string;
            locale: "en" | "ru";
        }, {
            value: string;
            locale: "en" | "ru";
        }>, "many">, z.ZodArray<z.ZodObject<{
            locale: z.ZodEnum<["en", "ru"]>;
            value: z.ZodString;
        }, "strip", z.ZodTypeAny, {
            value: string;
            locale: "en" | "ru";
        }, {
            value: string;
            locale: "en" | "ru";
        }>, "many">]>;
        image: z.ZodNullable<z.ZodString>;
    }, "strip", z.ZodTypeAny, {
        name: {
            value: string;
            locale: "en" | "ru";
        }[];
        image: string | null;
        actorType: "user";
        actorId: string;
    }, {
        name: {
            value: string;
            locale: "en" | "ru";
        }[];
        image: string | null;
        actorType: "user";
        actorId: string;
    }>;
    memberCount: z.ZodNumber;
    image: z.ZodNullable<z.ZodString>;
    createdAt: z.ZodDate;
    updatedAt: z.ZodDate;
    deletedAt: z.ZodOptional<z.ZodNullable<z.ZodDate>>;
}, "strip", z.ZodTypeAny, {
    id: string;
    createdAt: Date;
    updatedAt: Date;
    name: {
        value: string;
        locale: "en" | "ru";
    }[];
    image: string | null;
    description: {
        value: string;
        locale: "en" | "ru";
    }[];
    headMember: {
        name: {
            value: string;
            locale: "en" | "ru";
        }[];
        image: string | null;
        actorType: "user";
        actorId: string;
    };
    memberCount: number;
    deletedAt?: Date | null | undefined;
}, {
    id: string;
    createdAt: Date;
    updatedAt: Date;
    name: {
        value: string;
        locale: "en" | "ru";
    }[];
    image: string | null;
    description: {
        value: string;
        locale: "en" | "ru";
    }[];
    headMember: {
        name: {
            value: string;
            locale: "en" | "ru";
        }[];
        image: string | null;
        actorType: "user";
        actorId: string;
    };
    memberCount: number;
    deletedAt?: Date | null | undefined;
}>, "many">;
type CreateCommuneInput = Infer<typeof CreateCommuneInputSchema>;
declare const CreateCommuneInputSchema: z.ZodObject<{
    headUserId: z.ZodOptional<z.ZodString>;
    name: z.ZodArray<z.ZodObject<{
        locale: z.ZodEnum<["en", "ru"]>;
        value: z.ZodString;
    }, "strip", z.ZodTypeAny, {
        value: string;
        locale: "en" | "ru";
    }, {
        value: string;
        locale: "en" | "ru";
    }>, "many">;
    description: z.ZodArray<z.ZodObject<{
        locale: z.ZodEnum<["en", "ru"]>;
        value: z.ZodString;
    }, "strip", z.ZodTypeAny, {
        value: string;
        locale: "en" | "ru";
    }, {
        value: string;
        locale: "en" | "ru";
    }>, "many">;
}, "strip", z.ZodTypeAny, {
    name: {
        value: string;
        locale: "en" | "ru";
    }[];
    description: {
        value: string;
        locale: "en" | "ru";
    }[];
    headUserId?: string | undefined;
}, {
    name: {
        value: string;
        locale: "en" | "ru";
    }[];
    description: {
        value: string;
        locale: "en" | "ru";
    }[];
    headUserId?: string | undefined;
}>;
type UpdateCommuneInput = Infer<typeof UpdateCommuneInputSchema>;
declare const UpdateCommuneInputSchema: z.ZodObject<{
    id: z.ZodString;
    name: z.ZodOptional<z.ZodArray<z.ZodObject<{
        locale: z.ZodEnum<["en", "ru"]>;
        value: z.ZodString;
    }, "strip", z.ZodTypeAny, {
        value: string;
        locale: "en" | "ru";
    }, {
        value: string;
        locale: "en" | "ru";
    }>, "many">>;
    description: z.ZodOptional<z.ZodArray<z.ZodObject<{
        locale: z.ZodEnum<["en", "ru"]>;
        value: z.ZodString;
    }, "strip", z.ZodTypeAny, {
        value: string;
        locale: "en" | "ru";
    }, {
        value: string;
        locale: "en" | "ru";
    }>, "many">>;
}, "strip", z.ZodTypeAny, {
    id: string;
    name?: {
        value: string;
        locale: "en" | "ru";
    }[] | undefined;
    description?: {
        value: string;
        locale: "en" | "ru";
    }[] | undefined;
}, {
    id: string;
    name?: {
        value: string;
        locale: "en" | "ru";
    }[] | undefined;
    description?: {
        value: string;
        locale: "en" | "ru";
    }[] | undefined;
}>;
type GetCommuneMembersInput = Infer<typeof GetCommuneMembersInputSchema>;
declare const GetCommuneMembersInputSchema: z.ZodObject<{
    pagination: z.ZodDefault<z.ZodObject<{
        page: z.ZodDefault<z.ZodNumber>;
        size: z.ZodDefault<z.ZodNumber>;
    }, "strip", z.ZodTypeAny, {
        page: number;
        size: number;
    }, {
        page?: number | undefined;
        size?: number | undefined;
    }>>;
    communeId: z.ZodString;
}, "strip", z.ZodTypeAny, {
    pagination: {
        page: number;
        size: number;
    };
    communeId: string;
}, {
    communeId: string;
    pagination?: {
        page?: number | undefined;
        size?: number | undefined;
    } | undefined;
}>;
type GetCommuneMemberOutput = Infer<typeof GetCommuneMemberOutputSchema>;
declare const GetCommuneMemberOutputSchema: z.ZodObject<{
    id: z.ZodString;
    actorType: z.ZodEnum<["user"]>;
    actorId: z.ZodString;
    name: z.ZodUnion<[z.ZodArray<z.ZodObject<{
        locale: z.ZodEnum<["en", "ru"]>;
        value: z.ZodString;
    }, "strip", z.ZodTypeAny, {
        value: string;
        locale: "en" | "ru";
    }, {
        value: string;
        locale: "en" | "ru";
    }>, "many">, z.ZodArray<z.ZodObject<{
        locale: z.ZodEnum<["en", "ru"]>;
        value: z.ZodString;
    }, "strip", z.ZodTypeAny, {
        value: string;
        locale: "en" | "ru";
    }, {
        value: string;
        locale: "en" | "ru";
    }>, "many">]>;
    image: z.ZodNullable<z.ZodString>;
    createdAt: z.ZodDate;
    deletedAt: z.ZodNullable<z.ZodNullable<z.ZodDate>>;
}, "strip", z.ZodTypeAny, {
    id: string;
    createdAt: Date;
    name: {
        value: string;
        locale: "en" | "ru";
    }[];
    image: string | null;
    deletedAt: Date | null;
    actorType: "user";
    actorId: string;
}, {
    id: string;
    createdAt: Date;
    name: {
        value: string;
        locale: "en" | "ru";
    }[];
    image: string | null;
    deletedAt: Date | null;
    actorType: "user";
    actorId: string;
}>;
type GetCommuneMembersOutput = Infer<typeof GetCommuneMembersOutputSchema>;
declare const GetCommuneMembersOutputSchema: z.ZodArray<z.ZodObject<{
    id: z.ZodString;
    actorType: z.ZodEnum<["user"]>;
    actorId: z.ZodString;
    name: z.ZodUnion<[z.ZodArray<z.ZodObject<{
        locale: z.ZodEnum<["en", "ru"]>;
        value: z.ZodString;
    }, "strip", z.ZodTypeAny, {
        value: string;
        locale: "en" | "ru";
    }, {
        value: string;
        locale: "en" | "ru";
    }>, "many">, z.ZodArray<z.ZodObject<{
        locale: z.ZodEnum<["en", "ru"]>;
        value: z.ZodString;
    }, "strip", z.ZodTypeAny, {
        value: string;
        locale: "en" | "ru";
    }, {
        value: string;
        locale: "en" | "ru";
    }>, "many">]>;
    image: z.ZodNullable<z.ZodString>;
    createdAt: z.ZodDate;
    deletedAt: z.ZodNullable<z.ZodNullable<z.ZodDate>>;
}, "strip", z.ZodTypeAny, {
    id: string;
    createdAt: Date;
    name: {
        value: string;
        locale: "en" | "ru";
    }[];
    image: string | null;
    deletedAt: Date | null;
    actorType: "user";
    actorId: string;
}, {
    id: string;
    createdAt: Date;
    name: {
        value: string;
        locale: "en" | "ru";
    }[];
    image: string | null;
    deletedAt: Date | null;
    actorType: "user";
    actorId: string;
}>, "many">;
type CreateCommuneMemberInput = Infer<typeof CreateCommuneMemberInputSchema>;
declare const CreateCommuneMemberInputSchema: z.ZodObject<{
    communeId: z.ZodString;
    userId: z.ZodString;
}, "strip", z.ZodTypeAny, {
    userId: string;
    communeId: string;
}, {
    userId: string;
    communeId: string;
}>;
type CommuneInvitationStatus = Infer<typeof CommuneInvitationStatusSchema>;
declare const CommuneInvitationStatusSchema: z.ZodEnum<["pending", "accepted", "rejected", "expired"]>;
type GetCommuneInvitationsInput = Infer<typeof GetCommuneInvitationsInputSchema>;
declare const GetCommuneInvitationsInputSchema: z.ZodObject<{
    pagination: z.ZodDefault<z.ZodObject<{
        page: z.ZodDefault<z.ZodNumber>;
        size: z.ZodDefault<z.ZodNumber>;
    }, "strip", z.ZodTypeAny, {
        page: number;
        size: number;
    }, {
        page?: number | undefined;
        size?: number | undefined;
    }>>;
    communeId: z.ZodOptional<z.ZodString>;
}, "strip", z.ZodTypeAny, {
    pagination: {
        page: number;
        size: number;
    };
    communeId?: string | undefined;
}, {
    pagination?: {
        page?: number | undefined;
        size?: number | undefined;
    } | undefined;
    communeId?: string | undefined;
}>;
type GetCommuneInvitationsOutput = Infer<typeof GetCommuneInvitationsOutputSchema>;
declare const GetCommuneInvitationsOutputSchema: z.ZodArray<z.ZodObject<{
    id: z.ZodString;
    communeId: z.ZodString;
    userId: z.ZodString;
    status: z.ZodEnum<["pending", "accepted", "rejected", "expired"]>;
    createdAt: z.ZodDate;
    updatedAt: z.ZodDate;
}, "strip", z.ZodTypeAny, {
    status: "pending" | "accepted" | "rejected" | "expired";
    id: string;
    createdAt: Date;
    updatedAt: Date;
    userId: string;
    communeId: string;
}, {
    status: "pending" | "accepted" | "rejected" | "expired";
    id: string;
    createdAt: Date;
    updatedAt: Date;
    userId: string;
    communeId: string;
}>, "many">;
type CreateCommuneInvitationInput = Infer<typeof CreateCommuneInvitationInputSchema>;
declare const CreateCommuneInvitationInputSchema: z.ZodObject<{
    communeId: z.ZodString;
    userId: z.ZodString;
}, "strip", z.ZodTypeAny, {
    userId: string;
    communeId: string;
}, {
    userId: string;
    communeId: string;
}>;
type CommuneJoinRequestStatus = Infer<typeof CommuneJoinRequestStatusSchema>;
declare const CommuneJoinRequestStatusSchema: z.ZodEnum<["pending", "accepted", "rejected"]>;
type GetCommuneJoinRequestsInput = Infer<typeof GetCommuneJoinRequestsInputSchema>;
declare const GetCommuneJoinRequestsInputSchema: z.ZodObject<{
    pagination: z.ZodDefault<z.ZodObject<{
        page: z.ZodDefault<z.ZodNumber>;
        size: z.ZodDefault<z.ZodNumber>;
    }, "strip", z.ZodTypeAny, {
        page: number;
        size: number;
    }, {
        page?: number | undefined;
        size?: number | undefined;
    }>>;
    communeId: z.ZodOptional<z.ZodString>;
}, "strip", z.ZodTypeAny, {
    pagination: {
        page: number;
        size: number;
    };
    communeId?: string | undefined;
}, {
    pagination?: {
        page?: number | undefined;
        size?: number | undefined;
    } | undefined;
    communeId?: string | undefined;
}>;
type GetCommuneJoinRequestsOutput = Infer<typeof GetCommuneJoinRequestsOutputSchema>;
declare const GetCommuneJoinRequestsOutputSchema: z.ZodArray<z.ZodObject<{
    id: z.ZodString;
    communeId: z.ZodString;
    userId: z.ZodString;
    status: z.ZodEnum<["pending", "accepted", "rejected"]>;
    createdAt: z.ZodDate;
    updatedAt: z.ZodDate;
}, "strip", z.ZodTypeAny, {
    status: "pending" | "accepted" | "rejected";
    id: string;
    createdAt: Date;
    updatedAt: Date;
    userId: string;
    communeId: string;
}, {
    status: "pending" | "accepted" | "rejected";
    id: string;
    createdAt: Date;
    updatedAt: Date;
    userId: string;
    communeId: string;
}>, "many">;
type CreateCommuneJoinRequestInput = Infer<typeof CreateCommuneJoinRequestInputSchema>;
declare const CreateCommuneJoinRequestInputSchema: z.ZodObject<{
    communeId: z.ZodString;
    userId: z.ZodString;
}, "strip", z.ZodTypeAny, {
    userId: string;
    communeId: string;
}, {
    userId: string;
    communeId: string;
}>;

type commune_CommuneInvitationStatus = CommuneInvitationStatus;
declare const commune_CommuneInvitationStatusSchema: typeof CommuneInvitationStatusSchema;
type commune_CommuneJoinRequestStatus = CommuneJoinRequestStatus;
declare const commune_CommuneJoinRequestStatusSchema: typeof CommuneJoinRequestStatusSchema;
type commune_CommuneMemberType = CommuneMemberType;
declare const commune_CommuneMemberTypeSchema: typeof CommuneMemberTypeSchema;
type commune_CreateCommuneInput = CreateCommuneInput;
declare const commune_CreateCommuneInputSchema: typeof CreateCommuneInputSchema;
type commune_CreateCommuneInvitationInput = CreateCommuneInvitationInput;
declare const commune_CreateCommuneInvitationInputSchema: typeof CreateCommuneInvitationInputSchema;
type commune_CreateCommuneJoinRequestInput = CreateCommuneJoinRequestInput;
declare const commune_CreateCommuneJoinRequestInputSchema: typeof CreateCommuneJoinRequestInputSchema;
type commune_CreateCommuneMemberInput = CreateCommuneMemberInput;
declare const commune_CreateCommuneMemberInputSchema: typeof CreateCommuneMemberInputSchema;
type commune_GetCommuneInvitationsInput = GetCommuneInvitationsInput;
declare const commune_GetCommuneInvitationsInputSchema: typeof GetCommuneInvitationsInputSchema;
type commune_GetCommuneInvitationsOutput = GetCommuneInvitationsOutput;
declare const commune_GetCommuneInvitationsOutputSchema: typeof GetCommuneInvitationsOutputSchema;
type commune_GetCommuneJoinRequestsInput = GetCommuneJoinRequestsInput;
declare const commune_GetCommuneJoinRequestsInputSchema: typeof GetCommuneJoinRequestsInputSchema;
type commune_GetCommuneJoinRequestsOutput = GetCommuneJoinRequestsOutput;
declare const commune_GetCommuneJoinRequestsOutputSchema: typeof GetCommuneJoinRequestsOutputSchema;
type commune_GetCommuneMemberOutput = GetCommuneMemberOutput;
declare const commune_GetCommuneMemberOutputSchema: typeof GetCommuneMemberOutputSchema;
type commune_GetCommuneMembersInput = GetCommuneMembersInput;
declare const commune_GetCommuneMembersInputSchema: typeof GetCommuneMembersInputSchema;
type commune_GetCommuneMembersOutput = GetCommuneMembersOutput;
declare const commune_GetCommuneMembersOutputSchema: typeof GetCommuneMembersOutputSchema;
type commune_GetCommuneOutput = GetCommuneOutput;
declare const commune_GetCommuneOutputSchema: typeof GetCommuneOutputSchema;
type commune_GetCommunesInput = GetCommunesInput;
declare const commune_GetCommunesInputSchema: typeof GetCommunesInputSchema;
type commune_GetCommunesOutput = GetCommunesOutput;
declare const commune_GetCommunesOutputSchema: typeof GetCommunesOutputSchema;
type commune_TransferHeadStatusInput = TransferHeadStatusInput;
declare const commune_TransferHeadStatusInputSchema: typeof TransferHeadStatusInputSchema;
type commune_UpdateCommuneInput = UpdateCommuneInput;
declare const commune_UpdateCommuneInputSchema: typeof UpdateCommuneInputSchema;
declare const commune_communeDescription: typeof communeDescription;
declare const commune_communeMemberActorType: typeof communeMemberActorType;
declare const commune_communeMemberName: typeof communeMemberName;
declare const commune_communeName: typeof communeName;
declare namespace commune {
  export { type commune_CommuneInvitationStatus as CommuneInvitationStatus, commune_CommuneInvitationStatusSchema as CommuneInvitationStatusSchema, type commune_CommuneJoinRequestStatus as CommuneJoinRequestStatus, commune_CommuneJoinRequestStatusSchema as CommuneJoinRequestStatusSchema, type commune_CommuneMemberType as CommuneMemberType, commune_CommuneMemberTypeSchema as CommuneMemberTypeSchema, type commune_CreateCommuneInput as CreateCommuneInput, commune_CreateCommuneInputSchema as CreateCommuneInputSchema, type commune_CreateCommuneInvitationInput as CreateCommuneInvitationInput, commune_CreateCommuneInvitationInputSchema as CreateCommuneInvitationInputSchema, type commune_CreateCommuneJoinRequestInput as CreateCommuneJoinRequestInput, commune_CreateCommuneJoinRequestInputSchema as CreateCommuneJoinRequestInputSchema, type commune_CreateCommuneMemberInput as CreateCommuneMemberInput, commune_CreateCommuneMemberInputSchema as CreateCommuneMemberInputSchema, type commune_GetCommuneInvitationsInput as GetCommuneInvitationsInput, commune_GetCommuneInvitationsInputSchema as GetCommuneInvitationsInputSchema, type commune_GetCommuneInvitationsOutput as GetCommuneInvitationsOutput, commune_GetCommuneInvitationsOutputSchema as GetCommuneInvitationsOutputSchema, type commune_GetCommuneJoinRequestsInput as GetCommuneJoinRequestsInput, commune_GetCommuneJoinRequestsInputSchema as GetCommuneJoinRequestsInputSchema, type commune_GetCommuneJoinRequestsOutput as GetCommuneJoinRequestsOutput, commune_GetCommuneJoinRequestsOutputSchema as GetCommuneJoinRequestsOutputSchema, type commune_GetCommuneMemberOutput as GetCommuneMemberOutput, commune_GetCommuneMemberOutputSchema as GetCommuneMemberOutputSchema, type commune_GetCommuneMembersInput as GetCommuneMembersInput, commune_GetCommuneMembersInputSchema as GetCommuneMembersInputSchema, type commune_GetCommuneMembersOutput as GetCommuneMembersOutput, commune_GetCommuneMembersOutputSchema as GetCommuneMembersOutputSchema, type commune_GetCommuneOutput as GetCommuneOutput, commune_GetCommuneOutputSchema as GetCommuneOutputSchema, type commune_GetCommunesInput as GetCommunesInput, commune_GetCommunesInputSchema as GetCommunesInputSchema, type commune_GetCommunesOutput as GetCommunesOutput, commune_GetCommunesOutputSchema as GetCommunesOutputSchema, type commune_TransferHeadStatusInput as TransferHeadStatusInput, commune_TransferHeadStatusInputSchema as TransferHeadStatusInputSchema, type commune_UpdateCommuneInput as UpdateCommuneInput, commune_UpdateCommuneInputSchema as UpdateCommuneInputSchema, commune_communeDescription as communeDescription, commune_communeMemberActorType as communeMemberActorType, commune_communeMemberName as communeMemberName, commune_communeName as communeName };
}

type RatingType = Infer<typeof RatingTypeSchema>;
declare const RatingTypeSchema: z.ZodEnum<["like", "dislike"]>;
type Rating = Infer<typeof RatingSchema>;
declare const RatingSchema: z.ZodObject<{
    likes: z.ZodNumber;
    dislikes: z.ZodNumber;
    status: z.ZodNullable<z.ZodEnum<["like", "dislike"]>>;
}, "strip", z.ZodTypeAny, {
    status: "like" | "dislike" | null;
    likes: number;
    dislikes: number;
}, {
    status: "like" | "dislike" | null;
    likes: number;
    dislikes: number;
}>;
declare const hubName: z.ZodArray<z.ZodObject<{
    locale: z.ZodEnum<["en", "ru"]>;
    value: z.ZodString;
}, "strip", z.ZodTypeAny, {
    value: string;
    locale: "en" | "ru";
}, {
    value: string;
    locale: "en" | "ru";
}>, "many">;
declare const hubDescription: z.ZodArray<z.ZodObject<{
    locale: z.ZodEnum<["en", "ru"]>;
    value: z.ZodString;
}, "strip", z.ZodTypeAny, {
    value: string;
    locale: "en" | "ru";
}, {
    value: string;
    locale: "en" | "ru";
}>, "many">;
declare const hubImage: z.ZodNullable<z.ZodString>;
declare const communityName: z.ZodArray<z.ZodObject<{
    locale: z.ZodEnum<["en", "ru"]>;
    value: z.ZodString;
}, "strip", z.ZodTypeAny, {
    value: string;
    locale: "en" | "ru";
}, {
    value: string;
    locale: "en" | "ru";
}>, "many">;
declare const communityDescription: z.ZodArray<z.ZodObject<{
    locale: z.ZodEnum<["en", "ru"]>;
    value: z.ZodString;
}, "strip", z.ZodTypeAny, {
    value: string;
    locale: "en" | "ru";
}, {
    value: string;
    locale: "en" | "ru";
}>, "many">;
declare const communityImage: z.ZodNullable<z.ZodString>;
declare const postUsefulness: z.ZodNumber;
type PostUsefulness = Infer<typeof PostUsefulnessSchema>;
declare const PostUsefulnessSchema: z.ZodObject<{
    value: z.ZodNullable<z.ZodNumber>;
    count: z.ZodNumber;
    totalValue: z.ZodNullable<z.ZodNumber>;
}, "strip", z.ZodTypeAny, {
    value: number | null;
    count: number;
    totalValue: number | null;
}, {
    value: number | null;
    count: number;
    totalValue: number | null;
}>;
declare const postTitle: z.ZodArray<z.ZodObject<{
    locale: z.ZodEnum<["en", "ru"]>;
    value: z.ZodString;
}, "strip", z.ZodTypeAny, {
    value: string;
    locale: "en" | "ru";
}, {
    value: string;
    locale: "en" | "ru";
}>, "many">;
declare const postBody: z.ZodArray<z.ZodObject<{
    locale: z.ZodEnum<["en", "ru"]>;
    value: z.ZodString;
}, "strip", z.ZodTypeAny, {
    value: string;
    locale: "en" | "ru";
}, {
    value: string;
    locale: "en" | "ru";
}>, "many">;
type GetPostOutput = Infer<typeof GetPostsOutputSchema>;
declare const GetPostOutputSchema: z.ZodObject<{
    id: z.ZodString;
    hub: z.ZodNullable<z.ZodObject<{
        id: z.ZodString;
        name: z.ZodArray<z.ZodObject<{
            locale: z.ZodEnum<["en", "ru"]>;
            value: z.ZodString;
        }, "strip", z.ZodTypeAny, {
            value: string;
            locale: "en" | "ru";
        }, {
            value: string;
            locale: "en" | "ru";
        }>, "many">;
        image: z.ZodNullable<z.ZodString>;
    }, "strip", z.ZodTypeAny, {
        id: string;
        name: {
            value: string;
            locale: "en" | "ru";
        }[];
        image: string | null;
    }, {
        id: string;
        name: {
            value: string;
            locale: "en" | "ru";
        }[];
        image: string | null;
    }>>;
    community: z.ZodNullable<z.ZodObject<{
        id: z.ZodString;
        name: z.ZodArray<z.ZodObject<{
            locale: z.ZodEnum<["en", "ru"]>;
            value: z.ZodString;
        }, "strip", z.ZodTypeAny, {
            value: string;
            locale: "en" | "ru";
        }, {
            value: string;
            locale: "en" | "ru";
        }>, "many">;
        image: z.ZodNullable<z.ZodString>;
    }, "strip", z.ZodTypeAny, {
        id: string;
        name: {
            value: string;
            locale: "en" | "ru";
        }[];
        image: string | null;
    }, {
        id: string;
        name: {
            value: string;
            locale: "en" | "ru";
        }[];
        image: string | null;
    }>>;
    author: z.ZodObject<{
        id: z.ZodString;
        email: z.ZodString;
        name: z.ZodArray<z.ZodObject<{
            locale: z.ZodEnum<["en", "ru"]>;
            value: z.ZodString;
        }, "strip", z.ZodTypeAny, {
            value: string;
            locale: "en" | "ru";
        }, {
            value: string;
            locale: "en" | "ru";
        }>, "many">;
        image: z.ZodNullable<z.ZodString>;
    }, "strip", z.ZodTypeAny, {
        id: string;
        email: string;
        name: {
            value: string;
            locale: "en" | "ru";
        }[];
        image: string | null;
    }, {
        id: string;
        email: string;
        name: {
            value: string;
            locale: "en" | "ru";
        }[];
        image: string | null;
    }>;
    rating: z.ZodObject<{
        likes: z.ZodNumber;
        dislikes: z.ZodNumber;
        status: z.ZodNullable<z.ZodEnum<["like", "dislike"]>>;
    }, "strip", z.ZodTypeAny, {
        status: "like" | "dislike" | null;
        likes: number;
        dislikes: number;
    }, {
        status: "like" | "dislike" | null;
        likes: number;
        dislikes: number;
    }>;
    usefulness: z.ZodObject<{
        value: z.ZodNullable<z.ZodNumber>;
        count: z.ZodNumber;
        totalValue: z.ZodNullable<z.ZodNumber>;
    }, "strip", z.ZodTypeAny, {
        value: number | null;
        count: number;
        totalValue: number | null;
    }, {
        value: number | null;
        count: number;
        totalValue: number | null;
    }>;
    title: z.ZodArray<z.ZodObject<{
        locale: z.ZodEnum<["en", "ru"]>;
        value: z.ZodString;
    }, "strip", z.ZodTypeAny, {
        value: string;
        locale: "en" | "ru";
    }, {
        value: string;
        locale: "en" | "ru";
    }>, "many">;
    body: z.ZodArray<z.ZodObject<{
        locale: z.ZodEnum<["en", "ru"]>;
        value: z.ZodString;
    }, "strip", z.ZodTypeAny, {
        value: string;
        locale: "en" | "ru";
    }, {
        value: string;
        locale: "en" | "ru";
    }>, "many">;
    tags: z.ZodArray<z.ZodObject<{
        id: z.ZodString;
        name: z.ZodArray<z.ZodObject<{
            locale: z.ZodEnum<["en", "ru"]>;
            value: z.ZodString;
        }, "strip", z.ZodTypeAny, {
            value: string;
            locale: "en" | "ru";
        }, {
            value: string;
            locale: "en" | "ru";
        }>, "many">;
    }, "strip", z.ZodTypeAny, {
        id: string;
        name: {
            value: string;
            locale: "en" | "ru";
        }[];
    }, {
        id: string;
        name: {
            value: string;
            locale: "en" | "ru";
        }[];
    }>, "many">;
    createdAt: z.ZodDate;
    updatedAt: z.ZodDate;
    deletedAt: z.ZodOptional<z.ZodNullable<z.ZodDate>>;
}, "strip", z.ZodTypeAny, {
    id: string;
    createdAt: Date;
    updatedAt: Date;
    hub: {
        id: string;
        name: {
            value: string;
            locale: "en" | "ru";
        }[];
        image: string | null;
    } | null;
    community: {
        id: string;
        name: {
            value: string;
            locale: "en" | "ru";
        }[];
        image: string | null;
    } | null;
    author: {
        id: string;
        email: string;
        name: {
            value: string;
            locale: "en" | "ru";
        }[];
        image: string | null;
    };
    rating: {
        status: "like" | "dislike" | null;
        likes: number;
        dislikes: number;
    };
    usefulness: {
        value: number | null;
        count: number;
        totalValue: number | null;
    };
    title: {
        value: string;
        locale: "en" | "ru";
    }[];
    body: {
        value: string;
        locale: "en" | "ru";
    }[];
    tags: {
        id: string;
        name: {
            value: string;
            locale: "en" | "ru";
        }[];
    }[];
    deletedAt?: Date | null | undefined;
}, {
    id: string;
    createdAt: Date;
    updatedAt: Date;
    hub: {
        id: string;
        name: {
            value: string;
            locale: "en" | "ru";
        }[];
        image: string | null;
    } | null;
    community: {
        id: string;
        name: {
            value: string;
            locale: "en" | "ru";
        }[];
        image: string | null;
    } | null;
    author: {
        id: string;
        email: string;
        name: {
            value: string;
            locale: "en" | "ru";
        }[];
        image: string | null;
    };
    rating: {
        status: "like" | "dislike" | null;
        likes: number;
        dislikes: number;
    };
    usefulness: {
        value: number | null;
        count: number;
        totalValue: number | null;
    };
    title: {
        value: string;
        locale: "en" | "ru";
    }[];
    body: {
        value: string;
        locale: "en" | "ru";
    }[];
    tags: {
        id: string;
        name: {
            value: string;
            locale: "en" | "ru";
        }[];
    }[];
    deletedAt?: Date | null | undefined;
}>;
type GetPostsInput = Infer<typeof GetPostsInputSchema>;
declare const GetPostsInputSchema: z.ZodObject<{
    pagination: z.ZodDefault<z.ZodObject<{
        page: z.ZodDefault<z.ZodNumber>;
        size: z.ZodDefault<z.ZodNumber>;
    }, "strip", z.ZodTypeAny, {
        page: number;
        size: number;
    }, {
        page?: number | undefined;
        size?: number | undefined;
    }>>;
    id: z.ZodOptional<z.ZodString>;
    lensId: z.ZodNullable<z.ZodString>;
}, "strip", z.ZodTypeAny, {
    pagination: {
        page: number;
        size: number;
    };
    lensId: string | null;
    id?: string | undefined;
}, {
    lensId: string | null;
    id?: string | undefined;
    pagination?: {
        page?: number | undefined;
        size?: number | undefined;
    } | undefined;
}>;
type GetPostsOutput = Infer<typeof GetPostsOutputSchema>;
declare const GetPostsOutputSchema: z.ZodArray<z.ZodObject<{
    id: z.ZodString;
    hub: z.ZodNullable<z.ZodObject<{
        id: z.ZodString;
        name: z.ZodArray<z.ZodObject<{
            locale: z.ZodEnum<["en", "ru"]>;
            value: z.ZodString;
        }, "strip", z.ZodTypeAny, {
            value: string;
            locale: "en" | "ru";
        }, {
            value: string;
            locale: "en" | "ru";
        }>, "many">;
        image: z.ZodNullable<z.ZodString>;
    }, "strip", z.ZodTypeAny, {
        id: string;
        name: {
            value: string;
            locale: "en" | "ru";
        }[];
        image: string | null;
    }, {
        id: string;
        name: {
            value: string;
            locale: "en" | "ru";
        }[];
        image: string | null;
    }>>;
    community: z.ZodNullable<z.ZodObject<{
        id: z.ZodString;
        name: z.ZodArray<z.ZodObject<{
            locale: z.ZodEnum<["en", "ru"]>;
            value: z.ZodString;
        }, "strip", z.ZodTypeAny, {
            value: string;
            locale: "en" | "ru";
        }, {
            value: string;
            locale: "en" | "ru";
        }>, "many">;
        image: z.ZodNullable<z.ZodString>;
    }, "strip", z.ZodTypeAny, {
        id: string;
        name: {
            value: string;
            locale: "en" | "ru";
        }[];
        image: string | null;
    }, {
        id: string;
        name: {
            value: string;
            locale: "en" | "ru";
        }[];
        image: string | null;
    }>>;
    author: z.ZodObject<{
        id: z.ZodString;
        email: z.ZodString;
        name: z.ZodArray<z.ZodObject<{
            locale: z.ZodEnum<["en", "ru"]>;
            value: z.ZodString;
        }, "strip", z.ZodTypeAny, {
            value: string;
            locale: "en" | "ru";
        }, {
            value: string;
            locale: "en" | "ru";
        }>, "many">;
        image: z.ZodNullable<z.ZodString>;
    }, "strip", z.ZodTypeAny, {
        id: string;
        email: string;
        name: {
            value: string;
            locale: "en" | "ru";
        }[];
        image: string | null;
    }, {
        id: string;
        email: string;
        name: {
            value: string;
            locale: "en" | "ru";
        }[];
        image: string | null;
    }>;
    rating: z.ZodObject<{
        likes: z.ZodNumber;
        dislikes: z.ZodNumber;
        status: z.ZodNullable<z.ZodEnum<["like", "dislike"]>>;
    }, "strip", z.ZodTypeAny, {
        status: "like" | "dislike" | null;
        likes: number;
        dislikes: number;
    }, {
        status: "like" | "dislike" | null;
        likes: number;
        dislikes: number;
    }>;
    usefulness: z.ZodObject<{
        value: z.ZodNullable<z.ZodNumber>;
        count: z.ZodNumber;
        totalValue: z.ZodNullable<z.ZodNumber>;
    }, "strip", z.ZodTypeAny, {
        value: number | null;
        count: number;
        totalValue: number | null;
    }, {
        value: number | null;
        count: number;
        totalValue: number | null;
    }>;
    title: z.ZodArray<z.ZodObject<{
        locale: z.ZodEnum<["en", "ru"]>;
        value: z.ZodString;
    }, "strip", z.ZodTypeAny, {
        value: string;
        locale: "en" | "ru";
    }, {
        value: string;
        locale: "en" | "ru";
    }>, "many">;
    body: z.ZodArray<z.ZodObject<{
        locale: z.ZodEnum<["en", "ru"]>;
        value: z.ZodString;
    }, "strip", z.ZodTypeAny, {
        value: string;
        locale: "en" | "ru";
    }, {
        value: string;
        locale: "en" | "ru";
    }>, "many">;
    tags: z.ZodArray<z.ZodObject<{
        id: z.ZodString;
        name: z.ZodArray<z.ZodObject<{
            locale: z.ZodEnum<["en", "ru"]>;
            value: z.ZodString;
        }, "strip", z.ZodTypeAny, {
            value: string;
            locale: "en" | "ru";
        }, {
            value: string;
            locale: "en" | "ru";
        }>, "many">;
    }, "strip", z.ZodTypeAny, {
        id: string;
        name: {
            value: string;
            locale: "en" | "ru";
        }[];
    }, {
        id: string;
        name: {
            value: string;
            locale: "en" | "ru";
        }[];
    }>, "many">;
    createdAt: z.ZodDate;
    updatedAt: z.ZodDate;
    deletedAt: z.ZodOptional<z.ZodNullable<z.ZodDate>>;
}, "strip", z.ZodTypeAny, {
    id: string;
    createdAt: Date;
    updatedAt: Date;
    hub: {
        id: string;
        name: {
            value: string;
            locale: "en" | "ru";
        }[];
        image: string | null;
    } | null;
    community: {
        id: string;
        name: {
            value: string;
            locale: "en" | "ru";
        }[];
        image: string | null;
    } | null;
    author: {
        id: string;
        email: string;
        name: {
            value: string;
            locale: "en" | "ru";
        }[];
        image: string | null;
    };
    rating: {
        status: "like" | "dislike" | null;
        likes: number;
        dislikes: number;
    };
    usefulness: {
        value: number | null;
        count: number;
        totalValue: number | null;
    };
    title: {
        value: string;
        locale: "en" | "ru";
    }[];
    body: {
        value: string;
        locale: "en" | "ru";
    }[];
    tags: {
        id: string;
        name: {
            value: string;
            locale: "en" | "ru";
        }[];
    }[];
    deletedAt?: Date | null | undefined;
}, {
    id: string;
    createdAt: Date;
    updatedAt: Date;
    hub: {
        id: string;
        name: {
            value: string;
            locale: "en" | "ru";
        }[];
        image: string | null;
    } | null;
    community: {
        id: string;
        name: {
            value: string;
            locale: "en" | "ru";
        }[];
        image: string | null;
    } | null;
    author: {
        id: string;
        email: string;
        name: {
            value: string;
            locale: "en" | "ru";
        }[];
        image: string | null;
    };
    rating: {
        status: "like" | "dislike" | null;
        likes: number;
        dislikes: number;
    };
    usefulness: {
        value: number | null;
        count: number;
        totalValue: number | null;
    };
    title: {
        value: string;
        locale: "en" | "ru";
    }[];
    body: {
        value: string;
        locale: "en" | "ru";
    }[];
    tags: {
        id: string;
        name: {
            value: string;
            locale: "en" | "ru";
        }[];
    }[];
    deletedAt?: Date | null | undefined;
}>, "many">;
type CreatePostInput = Infer<typeof CreatePostInputSchema>;
declare const CreatePostInputSchema: z.ZodObject<{
    hubId: z.ZodNullable<z.ZodString>;
    communityId: z.ZodNullable<z.ZodString>;
    title: z.ZodArray<z.ZodObject<{
        locale: z.ZodEnum<["en", "ru"]>;
        value: z.ZodString;
    }, "strip", z.ZodTypeAny, {
        value: string;
        locale: "en" | "ru";
    }, {
        value: string;
        locale: "en" | "ru";
    }>, "many">;
    body: z.ZodArray<z.ZodObject<{
        locale: z.ZodEnum<["en", "ru"]>;
        value: z.ZodString;
    }, "strip", z.ZodTypeAny, {
        value: string;
        locale: "en" | "ru";
    }, {
        value: string;
        locale: "en" | "ru";
    }>, "many">;
    tagIds: z.ZodArray<z.ZodString, "many">;
}, "strip", z.ZodTypeAny, {
    title: {
        value: string;
        locale: "en" | "ru";
    }[];
    body: {
        value: string;
        locale: "en" | "ru";
    }[];
    hubId: string | null;
    communityId: string | null;
    tagIds: string[];
}, {
    title: {
        value: string;
        locale: "en" | "ru";
    }[];
    body: {
        value: string;
        locale: "en" | "ru";
    }[];
    hubId: string | null;
    communityId: string | null;
    tagIds: string[];
}>;
type UpdatePostInput = Infer<typeof UpdatePostInputSchema>;
declare const UpdatePostInputSchema: z.ZodObject<{
    id: z.ZodString;
    title: z.ZodOptional<z.ZodArray<z.ZodObject<{
        locale: z.ZodEnum<["en", "ru"]>;
        value: z.ZodString;
    }, "strip", z.ZodTypeAny, {
        value: string;
        locale: "en" | "ru";
    }, {
        value: string;
        locale: "en" | "ru";
    }>, "many">>;
    body: z.ZodOptional<z.ZodArray<z.ZodObject<{
        locale: z.ZodEnum<["en", "ru"]>;
        value: z.ZodString;
    }, "strip", z.ZodTypeAny, {
        value: string;
        locale: "en" | "ru";
    }, {
        value: string;
        locale: "en" | "ru";
    }>, "many">>;
    tagIds: z.ZodOptional<z.ZodArray<z.ZodString, "many">>;
}, "strip", z.ZodTypeAny, {
    id: string;
    title?: {
        value: string;
        locale: "en" | "ru";
    }[] | undefined;
    body?: {
        value: string;
        locale: "en" | "ru";
    }[] | undefined;
    tagIds?: string[] | undefined;
}, {
    id: string;
    title?: {
        value: string;
        locale: "en" | "ru";
    }[] | undefined;
    body?: {
        value: string;
        locale: "en" | "ru";
    }[] | undefined;
    tagIds?: string[] | undefined;
}>;
type DeletePostInput = Infer<typeof DeletePostInputSchema>;
declare const DeletePostInputSchema: z.ZodObject<{
    id: z.ZodString;
    reason: z.ZodNullable<z.ZodString>;
}, "strip", z.ZodTypeAny, {
    id: string;
    reason: string | null;
}, {
    id: string;
    reason: string | null;
}>;
type UpdatePostRatingInput = Infer<typeof UpdatePostRatingInputSchema>;
declare const UpdatePostRatingInputSchema: z.ZodObject<{
    id: z.ZodString;
    type: z.ZodEnum<["like", "dislike"]>;
}, "strip", z.ZodTypeAny, {
    type: "like" | "dislike";
    id: string;
}, {
    type: "like" | "dislike";
    id: string;
}>;
type UpdatePostRatingOutput = Infer<typeof UpdatePostRatingOutputSchema>;
declare const UpdatePostRatingOutputSchema: z.ZodObject<{
    likes: z.ZodNumber;
    dislikes: z.ZodNumber;
    status: z.ZodNullable<z.ZodEnum<["like", "dislike"]>>;
}, "strip", z.ZodTypeAny, {
    status: "like" | "dislike" | null;
    likes: number;
    dislikes: number;
}, {
    status: "like" | "dislike" | null;
    likes: number;
    dislikes: number;
}>;
type UpdatePostUsefulnessInput = Infer<typeof UpdatePostUsefulnessInputSchema>;
declare const UpdatePostUsefulnessInputSchema: z.ZodObject<{
    id: z.ZodString;
    value: z.ZodNullable<z.ZodNumber>;
}, "strip", z.ZodTypeAny, {
    value: number | null;
    id: string;
}, {
    value: number | null;
    id: string;
}>;
type UpdatePostUsefulnessOutput = Infer<typeof UpdatePostUsefulnessOutputSchema>;
declare const UpdatePostUsefulnessOutputSchema: z.ZodObject<{
    value: z.ZodNullable<z.ZodNumber>;
    count: z.ZodNumber;
    totalValue: z.ZodNullable<z.ZodNumber>;
}, "strip", z.ZodTypeAny, {
    value: number | null;
    count: number;
    totalValue: number | null;
}, {
    value: number | null;
    count: number;
    totalValue: number | null;
}>;
type CommentEntityType = Infer<typeof CommentEntityTypeSchema>;
declare const CommentEntityTypeSchema: z.ZodEnum<["post", "comment"]>;
declare const commentBody: z.ZodArray<z.ZodObject<{
    locale: z.ZodEnum<["en", "ru"]>;
    value: z.ZodString;
}, "strip", z.ZodTypeAny, {
    value: string;
    locale: "en" | "ru";
}, {
    value: string;
    locale: "en" | "ru";
}>, "many">;
type GetCommentsInput = Infer<typeof GetCommentsInputSchema>;
declare const GetCommentsInputSchema: z.ZodUnion<[z.ZodObject<{
    id: z.ZodString;
    entityType: z.ZodOptional<z.ZodNever>;
    entityId: z.ZodOptional<z.ZodNever>;
}, "strip", z.ZodTypeAny, {
    id: string;
    entityType?: undefined;
    entityId?: undefined;
}, {
    id: string;
    entityType?: undefined;
    entityId?: undefined;
}>, z.ZodObject<{
    id: z.ZodOptional<z.ZodNever>;
    entityType: z.ZodEnum<["post", "comment"]>;
    entityId: z.ZodString;
}, "strip", z.ZodTypeAny, {
    entityType: "post" | "comment";
    entityId: string;
    id?: undefined;
}, {
    entityType: "post" | "comment";
    entityId: string;
    id?: undefined;
}>]>;
type GetCommentsOutput = Infer<typeof GetCommentsOutputSchema>;
declare const GetCommentsOutputSchema: z.ZodArray<z.ZodObject<{
    id: z.ZodString;
    path: z.ZodString;
    author: z.ZodNullable<z.ZodObject<{
        id: z.ZodString;
        email: z.ZodString;
        name: z.ZodArray<z.ZodObject<{
            locale: z.ZodEnum<["en", "ru"]>;
            value: z.ZodString;
        }, "strip", z.ZodTypeAny, {
            value: string;
            locale: "en" | "ru";
        }, {
            value: string;
            locale: "en" | "ru";
        }>, "many">;
        image: z.ZodNullable<z.ZodString>;
    }, "strip", z.ZodTypeAny, {
        id: string;
        email: string;
        name: {
            value: string;
            locale: "en" | "ru";
        }[];
        image: string | null;
    }, {
        id: string;
        email: string;
        name: {
            value: string;
            locale: "en" | "ru";
        }[];
        image: string | null;
    }>>;
    isAnonymous: z.ZodBoolean;
    anonimityReason: z.ZodNullable<z.ZodString>;
    rating: z.ZodObject<{
        likes: z.ZodNumber;
        dislikes: z.ZodNumber;
        status: z.ZodNullable<z.ZodEnum<["like", "dislike"]>>;
    }, "strip", z.ZodTypeAny, {
        status: "like" | "dislike" | null;
        likes: number;
        dislikes: number;
    }, {
        status: "like" | "dislike" | null;
        likes: number;
        dislikes: number;
    }>;
    body: z.ZodNullable<z.ZodArray<z.ZodObject<{
        locale: z.ZodEnum<["en", "ru"]>;
        value: z.ZodString;
    }, "strip", z.ZodTypeAny, {
        value: string;
        locale: "en" | "ru";
    }, {
        value: string;
        locale: "en" | "ru";
    }>, "many">>;
    childrenCount: z.ZodNumber;
    deleteReason: z.ZodNullable<z.ZodString>;
    createdAt: z.ZodDate;
    updatedAt: z.ZodDate;
    deletedAt: z.ZodNullable<z.ZodDate>;
}, "strip", z.ZodTypeAny, {
    path: string;
    id: string;
    createdAt: Date;
    updatedAt: Date;
    deletedAt: Date | null;
    author: {
        id: string;
        email: string;
        name: {
            value: string;
            locale: "en" | "ru";
        }[];
        image: string | null;
    } | null;
    rating: {
        status: "like" | "dislike" | null;
        likes: number;
        dislikes: number;
    };
    body: {
        value: string;
        locale: "en" | "ru";
    }[] | null;
    isAnonymous: boolean;
    anonimityReason: string | null;
    childrenCount: number;
    deleteReason: string | null;
}, {
    path: string;
    id: string;
    createdAt: Date;
    updatedAt: Date;
    deletedAt: Date | null;
    author: {
        id: string;
        email: string;
        name: {
            value: string;
            locale: "en" | "ru";
        }[];
        image: string | null;
    } | null;
    rating: {
        status: "like" | "dislike" | null;
        likes: number;
        dislikes: number;
    };
    body: {
        value: string;
        locale: "en" | "ru";
    }[] | null;
    isAnonymous: boolean;
    anonimityReason: string | null;
    childrenCount: number;
    deleteReason: string | null;
}>, "many">;
type CreateCommentInput = Infer<typeof CreateCommentInputSchema>;
declare const CreateCommentInputSchema: z.ZodObject<{
    entityType: z.ZodEnum<["post", "comment"]>;
    entityId: z.ZodString;
    body: z.ZodArray<z.ZodObject<{
        locale: z.ZodEnum<["en", "ru"]>;
        value: z.ZodString;
    }, "strip", z.ZodTypeAny, {
        value: string;
        locale: "en" | "ru";
    }, {
        value: string;
        locale: "en" | "ru";
    }>, "many">;
}, "strip", z.ZodTypeAny, {
    body: {
        value: string;
        locale: "en" | "ru";
    }[];
    entityType: "post" | "comment";
    entityId: string;
}, {
    body: {
        value: string;
        locale: "en" | "ru";
    }[];
    entityType: "post" | "comment";
    entityId: string;
}>;
type UpdateCommentInput = Infer<typeof UpdateCommentInputSchema>;
declare const UpdateCommentInputSchema: z.ZodObject<{
    id: z.ZodString;
    body: z.ZodOptional<z.ZodArray<z.ZodObject<{
        locale: z.ZodEnum<["en", "ru"]>;
        value: z.ZodString;
    }, "strip", z.ZodTypeAny, {
        value: string;
        locale: "en" | "ru";
    }, {
        value: string;
        locale: "en" | "ru";
    }>, "many">>;
}, "strip", z.ZodTypeAny, {
    id: string;
    body?: {
        value: string;
        locale: "en" | "ru";
    }[] | undefined;
}, {
    id: string;
    body?: {
        value: string;
        locale: "en" | "ru";
    }[] | undefined;
}>;
type DeleteCommentInput = Infer<typeof DeleteCommentInputSchema>;
declare const DeleteCommentInputSchema: z.ZodObject<{
    id: z.ZodString;
    reason: z.ZodNullable<z.ZodString>;
}, "strip", z.ZodTypeAny, {
    id: string;
    reason: string | null;
}, {
    id: string;
    reason: string | null;
}>;
type UpdateCommentRatingInput = Infer<typeof UpdateCommentRatingInputSchema>;
declare const UpdateCommentRatingInputSchema: z.ZodObject<{
    id: z.ZodString;
    type: z.ZodEnum<["like", "dislike"]>;
}, "strip", z.ZodTypeAny, {
    type: "like" | "dislike";
    id: string;
}, {
    type: "like" | "dislike";
    id: string;
}>;
type UpdateCommentRatingOutput = Infer<typeof UpdateCommentRatingOutputSchema>;
declare const UpdateCommentRatingOutputSchema: z.ZodObject<{
    likes: z.ZodNumber;
    dislikes: z.ZodNumber;
    status: z.ZodNullable<z.ZodEnum<["like", "dislike"]>>;
}, "strip", z.ZodTypeAny, {
    status: "like" | "dislike" | null;
    likes: number;
    dislikes: number;
}, {
    status: "like" | "dislike" | null;
    likes: number;
    dislikes: number;
}>;
type AnonimifyCommentInput = Infer<typeof AnonimifyCommentInputSchema>;
declare const AnonimifyCommentInputSchema: z.ZodObject<{
    id: z.ZodString;
    reason: z.ZodNullable<z.ZodString>;
}, "strip", z.ZodTypeAny, {
    id: string;
    reason: string | null;
}, {
    id: string;
    reason: string | null;
}>;
declare const lensName: z.ZodString;
declare const lensCode: z.ZodString;
type CreateLensInput = Infer<typeof CreateLensInputSchema>;
declare const CreateLensInputSchema: z.ZodObject<{
    name: z.ZodString;
    code: z.ZodString;
}, "strip", z.ZodTypeAny, {
    code: string;
    name: string;
}, {
    code: string;
    name: string;
}>;
type UpdateLensInput = Infer<typeof UpdateLensInputSchema>;
declare const UpdateLensInputSchema: z.ZodObject<{
    id: z.ZodString;
    name: z.ZodOptional<z.ZodString>;
    code: z.ZodOptional<z.ZodString>;
}, "strip", z.ZodTypeAny, {
    id: string;
    code?: string | undefined;
    name?: string | undefined;
}, {
    id: string;
    code?: string | undefined;
    name?: string | undefined;
}>;
type GetHubsInput = Infer<typeof GetHubsInputSchema>;
declare const GetHubsInputSchema: z.ZodObject<{
    pagination: z.ZodOptional<z.ZodDefault<z.ZodObject<{
        page: z.ZodDefault<z.ZodNumber>;
        size: z.ZodDefault<z.ZodNumber>;
    }, "strip", z.ZodTypeAny, {
        page: number;
        size: number;
    }, {
        page?: number | undefined;
        size?: number | undefined;
    }>>>;
    ids: z.ZodOptional<z.ZodArray<z.ZodString, "many">>;
    query: z.ZodOptional<z.ZodString>;
}, "strip", z.ZodTypeAny, {
    pagination?: {
        page: number;
        size: number;
    } | undefined;
    ids?: string[] | undefined;
    query?: string | undefined;
}, {
    pagination?: {
        page?: number | undefined;
        size?: number | undefined;
    } | undefined;
    ids?: string[] | undefined;
    query?: string | undefined;
}>;
type GetHubsOutput = Infer<typeof GetHubsOutputSchema>;
declare const GetHubsOutputSchema: z.ZodArray<z.ZodObject<{
    id: z.ZodString;
    headUser: z.ZodObject<{
        id: z.ZodString;
        email: z.ZodString;
        name: z.ZodArray<z.ZodObject<{
            locale: z.ZodEnum<["en", "ru"]>;
            value: z.ZodString;
        }, "strip", z.ZodTypeAny, {
            value: string;
            locale: "en" | "ru";
        }, {
            value: string;
            locale: "en" | "ru";
        }>, "many">;
        image: z.ZodNullable<z.ZodString>;
    }, "strip", z.ZodTypeAny, {
        id: string;
        email: string;
        name: {
            value: string;
            locale: "en" | "ru";
        }[];
        image: string | null;
    }, {
        id: string;
        email: string;
        name: {
            value: string;
            locale: "en" | "ru";
        }[];
        image: string | null;
    }>;
    image: z.ZodNullable<z.ZodString>;
    name: z.ZodArray<z.ZodObject<{
        locale: z.ZodEnum<["en", "ru"]>;
        value: z.ZodString;
    }, "strip", z.ZodTypeAny, {
        value: string;
        locale: "en" | "ru";
    }, {
        value: string;
        locale: "en" | "ru";
    }>, "many">;
    description: z.ZodArray<z.ZodObject<{
        locale: z.ZodEnum<["en", "ru"]>;
        value: z.ZodString;
    }, "strip", z.ZodTypeAny, {
        value: string;
        locale: "en" | "ru";
    }, {
        value: string;
        locale: "en" | "ru";
    }>, "many">;
    createdAt: z.ZodDate;
    updatedAt: z.ZodDate;
    deletedAt: z.ZodOptional<z.ZodNullable<z.ZodDate>>;
}, "strip", z.ZodTypeAny, {
    id: string;
    createdAt: Date;
    updatedAt: Date;
    name: {
        value: string;
        locale: "en" | "ru";
    }[];
    image: string | null;
    description: {
        value: string;
        locale: "en" | "ru";
    }[];
    headUser: {
        id: string;
        email: string;
        name: {
            value: string;
            locale: "en" | "ru";
        }[];
        image: string | null;
    };
    deletedAt?: Date | null | undefined;
}, {
    id: string;
    createdAt: Date;
    updatedAt: Date;
    name: {
        value: string;
        locale: "en" | "ru";
    }[];
    image: string | null;
    description: {
        value: string;
        locale: "en" | "ru";
    }[];
    headUser: {
        id: string;
        email: string;
        name: {
            value: string;
            locale: "en" | "ru";
        }[];
        image: string | null;
    };
    deletedAt?: Date | null | undefined;
}>, "many">;
type CreateHubInput = Infer<typeof CreateHubInputSchema>;
declare const CreateHubInputSchema: z.ZodObject<{
    headUserId: z.ZodNullable<z.ZodString>;
    name: z.ZodArray<z.ZodObject<{
        locale: z.ZodEnum<["en", "ru"]>;
        value: z.ZodString;
    }, "strip", z.ZodTypeAny, {
        value: string;
        locale: "en" | "ru";
    }, {
        value: string;
        locale: "en" | "ru";
    }>, "many">;
    description: z.ZodArray<z.ZodObject<{
        locale: z.ZodEnum<["en", "ru"]>;
        value: z.ZodString;
    }, "strip", z.ZodTypeAny, {
        value: string;
        locale: "en" | "ru";
    }, {
        value: string;
        locale: "en" | "ru";
    }>, "many">;
}, "strip", z.ZodTypeAny, {
    name: {
        value: string;
        locale: "en" | "ru";
    }[];
    description: {
        value: string;
        locale: "en" | "ru";
    }[];
    headUserId: string | null;
}, {
    name: {
        value: string;
        locale: "en" | "ru";
    }[];
    description: {
        value: string;
        locale: "en" | "ru";
    }[];
    headUserId: string | null;
}>;
type UpdateHubInput = Infer<typeof UpdateHubInputSchema>;
declare const UpdateHubInputSchema: z.ZodObject<{
    id: z.ZodString;
    name: z.ZodOptional<z.ZodArray<z.ZodObject<{
        locale: z.ZodEnum<["en", "ru"]>;
        value: z.ZodString;
    }, "strip", z.ZodTypeAny, {
        value: string;
        locale: "en" | "ru";
    }, {
        value: string;
        locale: "en" | "ru";
    }>, "many">>;
    description: z.ZodOptional<z.ZodArray<z.ZodObject<{
        locale: z.ZodEnum<["en", "ru"]>;
        value: z.ZodString;
    }, "strip", z.ZodTypeAny, {
        value: string;
        locale: "en" | "ru";
    }, {
        value: string;
        locale: "en" | "ru";
    }>, "many">>;
}, "strip", z.ZodTypeAny, {
    id: string;
    name?: {
        value: string;
        locale: "en" | "ru";
    }[] | undefined;
    description?: {
        value: string;
        locale: "en" | "ru";
    }[] | undefined;
}, {
    id: string;
    name?: {
        value: string;
        locale: "en" | "ru";
    }[] | undefined;
    description?: {
        value: string;
        locale: "en" | "ru";
    }[] | undefined;
}>;
type GetCommunitiesInput = Infer<typeof GetCommunitiesInputSchema>;
declare const GetCommunitiesInputSchema: z.ZodObject<{
    pagination: z.ZodOptional<z.ZodDefault<z.ZodObject<{
        page: z.ZodDefault<z.ZodNumber>;
        size: z.ZodDefault<z.ZodNumber>;
    }, "strip", z.ZodTypeAny, {
        page: number;
        size: number;
    }, {
        page?: number | undefined;
        size?: number | undefined;
    }>>>;
    ids: z.ZodOptional<z.ZodArray<z.ZodString, "many">>;
    query: z.ZodOptional<z.ZodString>;
    hubId: z.ZodOptional<z.ZodString>;
}, "strip", z.ZodTypeAny, {
    pagination?: {
        page: number;
        size: number;
    } | undefined;
    ids?: string[] | undefined;
    query?: string | undefined;
    hubId?: string | undefined;
}, {
    pagination?: {
        page?: number | undefined;
        size?: number | undefined;
    } | undefined;
    ids?: string[] | undefined;
    query?: string | undefined;
    hubId?: string | undefined;
}>;
type GetCommunitiesOutput = Infer<typeof GetCommunitiesOutputSchema>;
declare const GetCommunitiesOutputSchema: z.ZodArray<z.ZodObject<{
    id: z.ZodString;
    hub: z.ZodNullable<z.ZodObject<{
        id: z.ZodString;
        name: z.ZodArray<z.ZodObject<{
            locale: z.ZodEnum<["en", "ru"]>;
            value: z.ZodString;
        }, "strip", z.ZodTypeAny, {
            value: string;
            locale: "en" | "ru";
        }, {
            value: string;
            locale: "en" | "ru";
        }>, "many">;
        image: z.ZodNullable<z.ZodString>;
    }, "strip", z.ZodTypeAny, {
        id: string;
        name: {
            value: string;
            locale: "en" | "ru";
        }[];
        image: string | null;
    }, {
        id: string;
        name: {
            value: string;
            locale: "en" | "ru";
        }[];
        image: string | null;
    }>>;
    headUser: z.ZodObject<{
        id: z.ZodString;
        email: z.ZodString;
        name: z.ZodArray<z.ZodObject<{
            locale: z.ZodEnum<["en", "ru"]>;
            value: z.ZodString;
        }, "strip", z.ZodTypeAny, {
            value: string;
            locale: "en" | "ru";
        }, {
            value: string;
            locale: "en" | "ru";
        }>, "many">;
        image: z.ZodNullable<z.ZodString>;
    }, "strip", z.ZodTypeAny, {
        id: string;
        email: string;
        name: {
            value: string;
            locale: "en" | "ru";
        }[];
        image: string | null;
    }, {
        id: string;
        email: string;
        name: {
            value: string;
            locale: "en" | "ru";
        }[];
        image: string | null;
    }>;
    image: z.ZodNullable<z.ZodString>;
    name: z.ZodArray<z.ZodObject<{
        locale: z.ZodEnum<["en", "ru"]>;
        value: z.ZodString;
    }, "strip", z.ZodTypeAny, {
        value: string;
        locale: "en" | "ru";
    }, {
        value: string;
        locale: "en" | "ru";
    }>, "many">;
    description: z.ZodArray<z.ZodObject<{
        locale: z.ZodEnum<["en", "ru"]>;
        value: z.ZodString;
    }, "strip", z.ZodTypeAny, {
        value: string;
        locale: "en" | "ru";
    }, {
        value: string;
        locale: "en" | "ru";
    }>, "many">;
    createdAt: z.ZodDate;
    updatedAt: z.ZodDate;
    deletedAt: z.ZodOptional<z.ZodNullable<z.ZodDate>>;
}, "strip", z.ZodTypeAny, {
    id: string;
    createdAt: Date;
    updatedAt: Date;
    name: {
        value: string;
        locale: "en" | "ru";
    }[];
    image: string | null;
    description: {
        value: string;
        locale: "en" | "ru";
    }[];
    hub: {
        id: string;
        name: {
            value: string;
            locale: "en" | "ru";
        }[];
        image: string | null;
    } | null;
    headUser: {
        id: string;
        email: string;
        name: {
            value: string;
            locale: "en" | "ru";
        }[];
        image: string | null;
    };
    deletedAt?: Date | null | undefined;
}, {
    id: string;
    createdAt: Date;
    updatedAt: Date;
    name: {
        value: string;
        locale: "en" | "ru";
    }[];
    image: string | null;
    description: {
        value: string;
        locale: "en" | "ru";
    }[];
    hub: {
        id: string;
        name: {
            value: string;
            locale: "en" | "ru";
        }[];
        image: string | null;
    } | null;
    headUser: {
        id: string;
        email: string;
        name: {
            value: string;
            locale: "en" | "ru";
        }[];
        image: string | null;
    };
    deletedAt?: Date | null | undefined;
}>, "many">;
type CreateCommunityInput = Infer<typeof CreateCommunityInputSchema>;
declare const CreateCommunityInputSchema: z.ZodObject<{
    hubId: z.ZodNullable<z.ZodString>;
    headUserId: z.ZodNullable<z.ZodString>;
    name: z.ZodArray<z.ZodObject<{
        locale: z.ZodEnum<["en", "ru"]>;
        value: z.ZodString;
    }, "strip", z.ZodTypeAny, {
        value: string;
        locale: "en" | "ru";
    }, {
        value: string;
        locale: "en" | "ru";
    }>, "many">;
    description: z.ZodArray<z.ZodObject<{
        locale: z.ZodEnum<["en", "ru"]>;
        value: z.ZodString;
    }, "strip", z.ZodTypeAny, {
        value: string;
        locale: "en" | "ru";
    }, {
        value: string;
        locale: "en" | "ru";
    }>, "many">;
}, "strip", z.ZodTypeAny, {
    name: {
        value: string;
        locale: "en" | "ru";
    }[];
    description: {
        value: string;
        locale: "en" | "ru";
    }[];
    headUserId: string | null;
    hubId: string | null;
}, {
    name: {
        value: string;
        locale: "en" | "ru";
    }[];
    description: {
        value: string;
        locale: "en" | "ru";
    }[];
    headUserId: string | null;
    hubId: string | null;
}>;
type UpdateCommunityInput = Infer<typeof UpdateCommunityInputSchema>;
declare const UpdateCommunityInputSchema: z.ZodObject<{
    id: z.ZodString;
    name: z.ZodOptional<z.ZodArray<z.ZodObject<{
        locale: z.ZodEnum<["en", "ru"]>;
        value: z.ZodString;
    }, "strip", z.ZodTypeAny, {
        value: string;
        locale: "en" | "ru";
    }, {
        value: string;
        locale: "en" | "ru";
    }>, "many">>;
    description: z.ZodOptional<z.ZodArray<z.ZodObject<{
        locale: z.ZodEnum<["en", "ru"]>;
        value: z.ZodString;
    }, "strip", z.ZodTypeAny, {
        value: string;
        locale: "en" | "ru";
    }, {
        value: string;
        locale: "en" | "ru";
    }>, "many">>;
}, "strip", z.ZodTypeAny, {
    id: string;
    name?: {
        value: string;
        locale: "en" | "ru";
    }[] | undefined;
    description?: {
        value: string;
        locale: "en" | "ru";
    }[] | undefined;
}, {
    id: string;
    name?: {
        value: string;
        locale: "en" | "ru";
    }[] | undefined;
    description?: {
        value: string;
        locale: "en" | "ru";
    }[] | undefined;
}>;

type reactor_AnonimifyCommentInput = AnonimifyCommentInput;
declare const reactor_AnonimifyCommentInputSchema: typeof AnonimifyCommentInputSchema;
type reactor_CommentEntityType = CommentEntityType;
declare const reactor_CommentEntityTypeSchema: typeof CommentEntityTypeSchema;
type reactor_CreateCommentInput = CreateCommentInput;
declare const reactor_CreateCommentInputSchema: typeof CreateCommentInputSchema;
type reactor_CreateCommunityInput = CreateCommunityInput;
declare const reactor_CreateCommunityInputSchema: typeof CreateCommunityInputSchema;
type reactor_CreateHubInput = CreateHubInput;
declare const reactor_CreateHubInputSchema: typeof CreateHubInputSchema;
type reactor_CreateLensInput = CreateLensInput;
declare const reactor_CreateLensInputSchema: typeof CreateLensInputSchema;
type reactor_CreatePostInput = CreatePostInput;
declare const reactor_CreatePostInputSchema: typeof CreatePostInputSchema;
type reactor_DeleteCommentInput = DeleteCommentInput;
declare const reactor_DeleteCommentInputSchema: typeof DeleteCommentInputSchema;
type reactor_DeletePostInput = DeletePostInput;
declare const reactor_DeletePostInputSchema: typeof DeletePostInputSchema;
type reactor_GetCommentsInput = GetCommentsInput;
declare const reactor_GetCommentsInputSchema: typeof GetCommentsInputSchema;
type reactor_GetCommentsOutput = GetCommentsOutput;
declare const reactor_GetCommentsOutputSchema: typeof GetCommentsOutputSchema;
type reactor_GetCommunitiesInput = GetCommunitiesInput;
declare const reactor_GetCommunitiesInputSchema: typeof GetCommunitiesInputSchema;
type reactor_GetCommunitiesOutput = GetCommunitiesOutput;
declare const reactor_GetCommunitiesOutputSchema: typeof GetCommunitiesOutputSchema;
type reactor_GetHubsInput = GetHubsInput;
declare const reactor_GetHubsInputSchema: typeof GetHubsInputSchema;
type reactor_GetHubsOutput = GetHubsOutput;
declare const reactor_GetHubsOutputSchema: typeof GetHubsOutputSchema;
type reactor_GetPostOutput = GetPostOutput;
declare const reactor_GetPostOutputSchema: typeof GetPostOutputSchema;
type reactor_GetPostsInput = GetPostsInput;
declare const reactor_GetPostsInputSchema: typeof GetPostsInputSchema;
type reactor_GetPostsOutput = GetPostsOutput;
declare const reactor_GetPostsOutputSchema: typeof GetPostsOutputSchema;
type reactor_PostUsefulness = PostUsefulness;
declare const reactor_PostUsefulnessSchema: typeof PostUsefulnessSchema;
type reactor_Rating = Rating;
declare const reactor_RatingSchema: typeof RatingSchema;
type reactor_RatingType = RatingType;
declare const reactor_RatingTypeSchema: typeof RatingTypeSchema;
type reactor_UpdateCommentInput = UpdateCommentInput;
declare const reactor_UpdateCommentInputSchema: typeof UpdateCommentInputSchema;
type reactor_UpdateCommentRatingInput = UpdateCommentRatingInput;
declare const reactor_UpdateCommentRatingInputSchema: typeof UpdateCommentRatingInputSchema;
type reactor_UpdateCommentRatingOutput = UpdateCommentRatingOutput;
declare const reactor_UpdateCommentRatingOutputSchema: typeof UpdateCommentRatingOutputSchema;
type reactor_UpdateCommunityInput = UpdateCommunityInput;
declare const reactor_UpdateCommunityInputSchema: typeof UpdateCommunityInputSchema;
type reactor_UpdateHubInput = UpdateHubInput;
declare const reactor_UpdateHubInputSchema: typeof UpdateHubInputSchema;
type reactor_UpdateLensInput = UpdateLensInput;
declare const reactor_UpdateLensInputSchema: typeof UpdateLensInputSchema;
type reactor_UpdatePostInput = UpdatePostInput;
declare const reactor_UpdatePostInputSchema: typeof UpdatePostInputSchema;
type reactor_UpdatePostRatingInput = UpdatePostRatingInput;
declare const reactor_UpdatePostRatingInputSchema: typeof UpdatePostRatingInputSchema;
type reactor_UpdatePostRatingOutput = UpdatePostRatingOutput;
declare const reactor_UpdatePostRatingOutputSchema: typeof UpdatePostRatingOutputSchema;
type reactor_UpdatePostUsefulnessInput = UpdatePostUsefulnessInput;
declare const reactor_UpdatePostUsefulnessInputSchema: typeof UpdatePostUsefulnessInputSchema;
type reactor_UpdatePostUsefulnessOutput = UpdatePostUsefulnessOutput;
declare const reactor_UpdatePostUsefulnessOutputSchema: typeof UpdatePostUsefulnessOutputSchema;
declare const reactor_commentBody: typeof commentBody;
declare const reactor_communityDescription: typeof communityDescription;
declare const reactor_communityImage: typeof communityImage;
declare const reactor_communityName: typeof communityName;
declare const reactor_hubDescription: typeof hubDescription;
declare const reactor_hubImage: typeof hubImage;
declare const reactor_hubName: typeof hubName;
declare const reactor_lensCode: typeof lensCode;
declare const reactor_lensName: typeof lensName;
declare const reactor_postBody: typeof postBody;
declare const reactor_postTitle: typeof postTitle;
declare const reactor_postUsefulness: typeof postUsefulness;
declare namespace reactor {
  export { type reactor_AnonimifyCommentInput as AnonimifyCommentInput, reactor_AnonimifyCommentInputSchema as AnonimifyCommentInputSchema, type reactor_CommentEntityType as CommentEntityType, reactor_CommentEntityTypeSchema as CommentEntityTypeSchema, type reactor_CreateCommentInput as CreateCommentInput, reactor_CreateCommentInputSchema as CreateCommentInputSchema, type reactor_CreateCommunityInput as CreateCommunityInput, reactor_CreateCommunityInputSchema as CreateCommunityInputSchema, type reactor_CreateHubInput as CreateHubInput, reactor_CreateHubInputSchema as CreateHubInputSchema, type reactor_CreateLensInput as CreateLensInput, reactor_CreateLensInputSchema as CreateLensInputSchema, type reactor_CreatePostInput as CreatePostInput, reactor_CreatePostInputSchema as CreatePostInputSchema, type reactor_DeleteCommentInput as DeleteCommentInput, reactor_DeleteCommentInputSchema as DeleteCommentInputSchema, type reactor_DeletePostInput as DeletePostInput, reactor_DeletePostInputSchema as DeletePostInputSchema, type reactor_GetCommentsInput as GetCommentsInput, reactor_GetCommentsInputSchema as GetCommentsInputSchema, type reactor_GetCommentsOutput as GetCommentsOutput, reactor_GetCommentsOutputSchema as GetCommentsOutputSchema, type reactor_GetCommunitiesInput as GetCommunitiesInput, reactor_GetCommunitiesInputSchema as GetCommunitiesInputSchema, type reactor_GetCommunitiesOutput as GetCommunitiesOutput, reactor_GetCommunitiesOutputSchema as GetCommunitiesOutputSchema, type reactor_GetHubsInput as GetHubsInput, reactor_GetHubsInputSchema as GetHubsInputSchema, type reactor_GetHubsOutput as GetHubsOutput, reactor_GetHubsOutputSchema as GetHubsOutputSchema, type reactor_GetPostOutput as GetPostOutput, reactor_GetPostOutputSchema as GetPostOutputSchema, type reactor_GetPostsInput as GetPostsInput, reactor_GetPostsInputSchema as GetPostsInputSchema, type reactor_GetPostsOutput as GetPostsOutput, reactor_GetPostsOutputSchema as GetPostsOutputSchema, type reactor_PostUsefulness as PostUsefulness, reactor_PostUsefulnessSchema as PostUsefulnessSchema, type reactor_Rating as Rating, reactor_RatingSchema as RatingSchema, type reactor_RatingType as RatingType, reactor_RatingTypeSchema as RatingTypeSchema, type reactor_UpdateCommentInput as UpdateCommentInput, reactor_UpdateCommentInputSchema as UpdateCommentInputSchema, type reactor_UpdateCommentRatingInput as UpdateCommentRatingInput, reactor_UpdateCommentRatingInputSchema as UpdateCommentRatingInputSchema, type reactor_UpdateCommentRatingOutput as UpdateCommentRatingOutput, reactor_UpdateCommentRatingOutputSchema as UpdateCommentRatingOutputSchema, type reactor_UpdateCommunityInput as UpdateCommunityInput, reactor_UpdateCommunityInputSchema as UpdateCommunityInputSchema, type reactor_UpdateHubInput as UpdateHubInput, reactor_UpdateHubInputSchema as UpdateHubInputSchema, type reactor_UpdateLensInput as UpdateLensInput, reactor_UpdateLensInputSchema as UpdateLensInputSchema, type reactor_UpdatePostInput as UpdatePostInput, reactor_UpdatePostInputSchema as UpdatePostInputSchema, type reactor_UpdatePostRatingInput as UpdatePostRatingInput, reactor_UpdatePostRatingInputSchema as UpdatePostRatingInputSchema, type reactor_UpdatePostRatingOutput as UpdatePostRatingOutput, reactor_UpdatePostRatingOutputSchema as UpdatePostRatingOutputSchema, type reactor_UpdatePostUsefulnessInput as UpdatePostUsefulnessInput, reactor_UpdatePostUsefulnessInputSchema as UpdatePostUsefulnessInputSchema, type reactor_UpdatePostUsefulnessOutput as UpdatePostUsefulnessOutput, reactor_UpdatePostUsefulnessOutputSchema as UpdatePostUsefulnessOutputSchema, reactor_commentBody as commentBody, reactor_communityDescription as communityDescription, reactor_communityImage as communityImage, reactor_communityName as communityName, reactor_hubDescription as hubDescription, reactor_hubImage as hubImage, reactor_hubName as hubName, reactor_lensCode as lensCode, reactor_lensName as lensName, reactor_postBody as postBody, reactor_postTitle as postTitle, reactor_postUsefulness as postUsefulness };
}

declare const userName: z.ZodArray<z.ZodObject<{
    locale: z.ZodEnum<["en", "ru"]>;
    value: z.ZodString;
}, "strip", z.ZodTypeAny, {
    value: string;
    locale: "en" | "ru";
}, {
    value: string;
    locale: "en" | "ru";
}>, "many">;
declare const userDescription: z.ZodArray<z.ZodObject<{
    locale: z.ZodEnum<["en", "ru"]>;
    value: z.ZodString;
}, "strip", z.ZodTypeAny, {
    value: string;
    locale: "en" | "ru";
}, {
    value: string;
    locale: "en" | "ru";
}>, "many">;
declare const userImage: z.ZodNullable<z.ZodString>;
declare const userTitleName: z.ZodArray<z.ZodObject<{
    locale: z.ZodEnum<["en", "ru"]>;
    value: z.ZodString;
}, "strip", z.ZodTypeAny, {
    value: string;
    locale: "en" | "ru";
}, {
    value: string;
    locale: "en" | "ru";
}>, "many">;
declare const userTitleIsActive: z.ZodBoolean;
declare const userTitleColor: z.ZodNullable<z.ZodString>;
declare const userNoteText: z.ZodString;
type UserRole = Infer<typeof UserRoleSchema>;
declare const UserRoleSchema: z.ZodEnum<["admin", "moderator", "user"]>;
type SimpleUser = Infer<typeof SimpleUserSchema>;
declare const SimpleUserSchema: z.ZodObject<{
    id: z.ZodString;
    email: z.ZodString;
    name: z.ZodArray<z.ZodObject<{
        locale: z.ZodEnum<["en", "ru"]>;
        value: z.ZodString;
    }, "strip", z.ZodTypeAny, {
        value: string;
        locale: "en" | "ru";
    }, {
        value: string;
        locale: "en" | "ru";
    }>, "many">;
    image: z.ZodNullable<z.ZodString>;
}, "strip", z.ZodTypeAny, {
    id: string;
    email: string;
    name: {
        value: string;
        locale: "en" | "ru";
    }[];
    image: string | null;
}, {
    id: string;
    email: string;
    name: {
        value: string;
        locale: "en" | "ru";
    }[];
    image: string | null;
}>;
type GetMeOutput = Infer<typeof GetMeOutputSchema>;
declare const GetMeOutputSchema: z.ZodObject<{
    id: z.ZodString;
    email: z.ZodString;
    role: z.ZodEnum<["admin", "moderator", "user"]>;
    name: z.ZodArray<z.ZodObject<{
        locale: z.ZodEnum<["en", "ru"]>;
        value: z.ZodString;
    }, "strip", z.ZodTypeAny, {
        value: string;
        locale: "en" | "ru";
    }, {
        value: string;
        locale: "en" | "ru";
    }>, "many">;
    description: z.ZodArray<z.ZodObject<{
        locale: z.ZodEnum<["en", "ru"]>;
        value: z.ZodString;
    }, "strip", z.ZodTypeAny, {
        value: string;
        locale: "en" | "ru";
    }, {
        value: string;
        locale: "en" | "ru";
    }>, "many">;
    image: z.ZodNullable<z.ZodString>;
    createdAt: z.ZodDate;
    updatedAt: z.ZodDate;
}, "strip", z.ZodTypeAny, {
    id: string;
    createdAt: Date;
    updatedAt: Date;
    email: string;
    name: {
        value: string;
        locale: "en" | "ru";
    }[];
    image: string | null;
    role: "admin" | "moderator" | "user";
    description: {
        value: string;
        locale: "en" | "ru";
    }[];
}, {
    id: string;
    createdAt: Date;
    updatedAt: Date;
    email: string;
    name: {
        value: string;
        locale: "en" | "ru";
    }[];
    image: string | null;
    role: "admin" | "moderator" | "user";
    description: {
        value: string;
        locale: "en" | "ru";
    }[];
}>;
type GetUsersInput = Infer<typeof GetUsersInputSchema>;
declare const GetUsersInputSchema: z.ZodObject<{
    pagination: z.ZodOptional<z.ZodDefault<z.ZodObject<{
        page: z.ZodDefault<z.ZodNumber>;
        size: z.ZodDefault<z.ZodNumber>;
    }, "strip", z.ZodTypeAny, {
        page: number;
        size: number;
    }, {
        page?: number | undefined;
        size?: number | undefined;
    }>>>;
    ids: z.ZodOptional<z.ZodArray<z.ZodString, "many">>;
    query: z.ZodOptional<z.ZodString>;
}, "strip", z.ZodTypeAny, {
    pagination?: {
        page: number;
        size: number;
    } | undefined;
    ids?: string[] | undefined;
    query?: string | undefined;
}, {
    pagination?: {
        page?: number | undefined;
        size?: number | undefined;
    } | undefined;
    ids?: string[] | undefined;
    query?: string | undefined;
}>;
type GetUserOutput = Infer<typeof GetUserOutputSchema>;
declare const GetUserOutputSchema: z.ZodObject<{
    id: z.ZodString;
    email: z.ZodString;
    role: z.ZodEnum<["admin", "moderator", "user"]>;
    name: z.ZodArray<z.ZodObject<{
        locale: z.ZodEnum<["en", "ru"]>;
        value: z.ZodString;
    }, "strip", z.ZodTypeAny, {
        value: string;
        locale: "en" | "ru";
    }, {
        value: string;
        locale: "en" | "ru";
    }>, "many">;
    description: z.ZodArray<z.ZodObject<{
        locale: z.ZodEnum<["en", "ru"]>;
        value: z.ZodString;
    }, "strip", z.ZodTypeAny, {
        value: string;
        locale: "en" | "ru";
    }, {
        value: string;
        locale: "en" | "ru";
    }>, "many">;
    image: z.ZodNullable<z.ZodString>;
    createdAt: z.ZodDate;
    updatedAt: z.ZodDate;
    deletedAt: z.ZodOptional<z.ZodNullable<z.ZodDate>>;
}, "strip", z.ZodTypeAny, {
    id: string;
    createdAt: Date;
    updatedAt: Date;
    email: string;
    name: {
        value: string;
        locale: "en" | "ru";
    }[];
    image: string | null;
    role: "admin" | "moderator" | "user";
    description: {
        value: string;
        locale: "en" | "ru";
    }[];
    deletedAt?: Date | null | undefined;
}, {
    id: string;
    createdAt: Date;
    updatedAt: Date;
    email: string;
    name: {
        value: string;
        locale: "en" | "ru";
    }[];
    image: string | null;
    role: "admin" | "moderator" | "user";
    description: {
        value: string;
        locale: "en" | "ru";
    }[];
    deletedAt?: Date | null | undefined;
}>;
type GetUsersOutput = Infer<typeof GetUsersOutputSchema>;
declare const GetUsersOutputSchema: z.ZodArray<z.ZodObject<{
    id: z.ZodString;
    email: z.ZodString;
    role: z.ZodEnum<["admin", "moderator", "user"]>;
    name: z.ZodArray<z.ZodObject<{
        locale: z.ZodEnum<["en", "ru"]>;
        value: z.ZodString;
    }, "strip", z.ZodTypeAny, {
        value: string;
        locale: "en" | "ru";
    }, {
        value: string;
        locale: "en" | "ru";
    }>, "many">;
    description: z.ZodArray<z.ZodObject<{
        locale: z.ZodEnum<["en", "ru"]>;
        value: z.ZodString;
    }, "strip", z.ZodTypeAny, {
        value: string;
        locale: "en" | "ru";
    }, {
        value: string;
        locale: "en" | "ru";
    }>, "many">;
    image: z.ZodNullable<z.ZodString>;
    createdAt: z.ZodDate;
    updatedAt: z.ZodDate;
    deletedAt: z.ZodOptional<z.ZodNullable<z.ZodDate>>;
}, "strip", z.ZodTypeAny, {
    id: string;
    createdAt: Date;
    updatedAt: Date;
    email: string;
    name: {
        value: string;
        locale: "en" | "ru";
    }[];
    image: string | null;
    role: "admin" | "moderator" | "user";
    description: {
        value: string;
        locale: "en" | "ru";
    }[];
    deletedAt?: Date | null | undefined;
}, {
    id: string;
    createdAt: Date;
    updatedAt: Date;
    email: string;
    name: {
        value: string;
        locale: "en" | "ru";
    }[];
    image: string | null;
    role: "admin" | "moderator" | "user";
    description: {
        value: string;
        locale: "en" | "ru";
    }[];
    deletedAt?: Date | null | undefined;
}>, "many">;
type UpdateUserInput = Infer<typeof UpdateUserInputSchema>;
declare const UpdateUserInputSchema: z.ZodObject<{
    id: z.ZodString;
    name: z.ZodOptional<z.ZodArray<z.ZodObject<{
        locale: z.ZodEnum<["en", "ru"]>;
        value: z.ZodString;
    }, "strip", z.ZodTypeAny, {
        value: string;
        locale: "en" | "ru";
    }, {
        value: string;
        locale: "en" | "ru";
    }>, "many">>;
    description: z.ZodOptional<z.ZodArray<z.ZodObject<{
        locale: z.ZodEnum<["en", "ru"]>;
        value: z.ZodString;
    }, "strip", z.ZodTypeAny, {
        value: string;
        locale: "en" | "ru";
    }, {
        value: string;
        locale: "en" | "ru";
    }>, "many">>;
}, "strip", z.ZodTypeAny, {
    id: string;
    name?: {
        value: string;
        locale: "en" | "ru";
    }[] | undefined;
    description?: {
        value: string;
        locale: "en" | "ru";
    }[] | undefined;
}, {
    id: string;
    name?: {
        value: string;
        locale: "en" | "ru";
    }[] | undefined;
    description?: {
        value: string;
        locale: "en" | "ru";
    }[] | undefined;
}>;
type CreateUserTitleInput = Infer<typeof CreateUserTitleInputSchema>;
declare const CreateUserTitleInputSchema: z.ZodObject<{
    userId: z.ZodString;
    name: z.ZodArray<z.ZodObject<{
        locale: z.ZodEnum<["en", "ru"]>;
        value: z.ZodString;
    }, "strip", z.ZodTypeAny, {
        value: string;
        locale: "en" | "ru";
    }, {
        value: string;
        locale: "en" | "ru";
    }>, "many">;
    isActive: z.ZodBoolean;
    color: z.ZodNullable<z.ZodString>;
}, "strip", z.ZodTypeAny, {
    name: {
        value: string;
        locale: "en" | "ru";
    }[];
    userId: string;
    isActive: boolean;
    color: string | null;
}, {
    name: {
        value: string;
        locale: "en" | "ru";
    }[];
    userId: string;
    isActive: boolean;
    color: string | null;
}>;
type UpdateUserTitleInput = Infer<typeof UpdateUserTitleInputSchema>;
declare const UpdateUserTitleInputSchema: z.ZodObject<{
    id: z.ZodString;
    name: z.ZodOptional<z.ZodArray<z.ZodObject<{
        locale: z.ZodEnum<["en", "ru"]>;
        value: z.ZodString;
    }, "strip", z.ZodTypeAny, {
        value: string;
        locale: "en" | "ru";
    }, {
        value: string;
        locale: "en" | "ru";
    }>, "many">>;
    isActive: z.ZodOptional<z.ZodBoolean>;
    color: z.ZodOptional<z.ZodNullable<z.ZodString>>;
}, "strip", z.ZodTypeAny, {
    id: string;
    name?: {
        value: string;
        locale: "en" | "ru";
    }[] | undefined;
    isActive?: boolean | undefined;
    color?: string | null | undefined;
}, {
    id: string;
    name?: {
        value: string;
        locale: "en" | "ru";
    }[] | undefined;
    isActive?: boolean | undefined;
    color?: string | null | undefined;
}>;
type GetUserTitlesInput = Infer<typeof GetUserTitlesInputSchema>;
declare const GetUserTitlesInputSchema: z.ZodObject<{
    userId: z.ZodString;
    ids: z.ZodOptional<z.ZodArray<z.ZodString, "many">>;
    isActive: z.ZodOptional<z.ZodBoolean>;
}, "strip", z.ZodTypeAny, {
    userId: string;
    ids?: string[] | undefined;
    isActive?: boolean | undefined;
}, {
    userId: string;
    ids?: string[] | undefined;
    isActive?: boolean | undefined;
}>;
type GetUserTitlesOutput = Infer<typeof GetUserTitlesOutputSchema>;
declare const GetUserTitlesOutputSchema: z.ZodArray<z.ZodObject<{
    id: z.ZodString;
    userId: z.ZodString;
    name: z.ZodArray<z.ZodObject<{
        locale: z.ZodEnum<["en", "ru"]>;
        value: z.ZodString;
    }, "strip", z.ZodTypeAny, {
        value: string;
        locale: "en" | "ru";
    }, {
        value: string;
        locale: "en" | "ru";
    }>, "many">;
    isActive: z.ZodBoolean;
    color: z.ZodNullable<z.ZodString>;
    createdAt: z.ZodDate;
    updatedAt: z.ZodDate;
    deletedAt: z.ZodOptional<z.ZodNullable<z.ZodDate>>;
}, "strip", z.ZodTypeAny, {
    id: string;
    createdAt: Date;
    updatedAt: Date;
    name: {
        value: string;
        locale: "en" | "ru";
    }[];
    userId: string;
    isActive: boolean;
    color: string | null;
    deletedAt?: Date | null | undefined;
}, {
    id: string;
    createdAt: Date;
    updatedAt: Date;
    name: {
        value: string;
        locale: "en" | "ru";
    }[];
    userId: string;
    isActive: boolean;
    color: string | null;
    deletedAt?: Date | null | undefined;
}>, "many">;
type GetUserNoteInput = Infer<typeof GetUserNoteInputSchema>;
declare const GetUserNoteInputSchema: z.ZodObject<{
    userId: z.ZodString;
}, "strip", z.ZodTypeAny, {
    userId: string;
}, {
    userId: string;
}>;
type GetUserNoteOutput = Infer<typeof GetUserNoteOutputSchema>;
declare const GetUserNoteOutputSchema: z.ZodObject<{
    text: z.ZodNullable<z.ZodString>;
}, "strip", z.ZodTypeAny, {
    text: string | null;
}, {
    text: string | null;
}>;
type UpdateUserNoteInput = Infer<typeof UpdateUserNoteInputSchema>;
declare const UpdateUserNoteInputSchema: z.ZodObject<{
    userId: z.ZodString;
    text: z.ZodNullable<z.ZodString>;
}, "strip", z.ZodTypeAny, {
    userId: string;
    text: string | null;
}, {
    userId: string;
    text: string | null;
}>;

type user_CreateUserTitleInput = CreateUserTitleInput;
declare const user_CreateUserTitleInputSchema: typeof CreateUserTitleInputSchema;
type user_GetMeOutput = GetMeOutput;
declare const user_GetMeOutputSchema: typeof GetMeOutputSchema;
type user_GetUserNoteInput = GetUserNoteInput;
declare const user_GetUserNoteInputSchema: typeof GetUserNoteInputSchema;
type user_GetUserNoteOutput = GetUserNoteOutput;
declare const user_GetUserNoteOutputSchema: typeof GetUserNoteOutputSchema;
type user_GetUserOutput = GetUserOutput;
declare const user_GetUserOutputSchema: typeof GetUserOutputSchema;
type user_GetUserTitlesInput = GetUserTitlesInput;
declare const user_GetUserTitlesInputSchema: typeof GetUserTitlesInputSchema;
type user_GetUserTitlesOutput = GetUserTitlesOutput;
declare const user_GetUserTitlesOutputSchema: typeof GetUserTitlesOutputSchema;
type user_GetUsersInput = GetUsersInput;
declare const user_GetUsersInputSchema: typeof GetUsersInputSchema;
type user_GetUsersOutput = GetUsersOutput;
declare const user_GetUsersOutputSchema: typeof GetUsersOutputSchema;
type user_SimpleUser = SimpleUser;
declare const user_SimpleUserSchema: typeof SimpleUserSchema;
type user_UpdateUserInput = UpdateUserInput;
declare const user_UpdateUserInputSchema: typeof UpdateUserInputSchema;
type user_UpdateUserNoteInput = UpdateUserNoteInput;
declare const user_UpdateUserNoteInputSchema: typeof UpdateUserNoteInputSchema;
type user_UpdateUserTitleInput = UpdateUserTitleInput;
declare const user_UpdateUserTitleInputSchema: typeof UpdateUserTitleInputSchema;
type user_UserRole = UserRole;
declare const user_UserRoleSchema: typeof UserRoleSchema;
declare const user_userDescription: typeof userDescription;
declare const user_userImage: typeof userImage;
declare const user_userName: typeof userName;
declare const user_userNoteText: typeof userNoteText;
declare const user_userTitleColor: typeof userTitleColor;
declare const user_userTitleIsActive: typeof userTitleIsActive;
declare const user_userTitleName: typeof userTitleName;
declare namespace user {
  export { type user_CreateUserTitleInput as CreateUserTitleInput, user_CreateUserTitleInputSchema as CreateUserTitleInputSchema, type user_GetMeOutput as GetMeOutput, user_GetMeOutputSchema as GetMeOutputSchema, type user_GetUserNoteInput as GetUserNoteInput, user_GetUserNoteInputSchema as GetUserNoteInputSchema, type user_GetUserNoteOutput as GetUserNoteOutput, user_GetUserNoteOutputSchema as GetUserNoteOutputSchema, type user_GetUserOutput as GetUserOutput, user_GetUserOutputSchema as GetUserOutputSchema, type user_GetUserTitlesInput as GetUserTitlesInput, user_GetUserTitlesInputSchema as GetUserTitlesInputSchema, type user_GetUserTitlesOutput as GetUserTitlesOutput, user_GetUserTitlesOutputSchema as GetUserTitlesOutputSchema, type user_GetUsersInput as GetUsersInput, user_GetUsersInputSchema as GetUsersInputSchema, type user_GetUsersOutput as GetUsersOutput, user_GetUsersOutputSchema as GetUsersOutputSchema, type user_SimpleUser as SimpleUser, user_SimpleUserSchema as SimpleUserSchema, type user_UpdateUserInput as UpdateUserInput, user_UpdateUserInputSchema as UpdateUserInputSchema, type user_UpdateUserNoteInput as UpdateUserNoteInput, user_UpdateUserNoteInputSchema as UpdateUserNoteInputSchema, type user_UpdateUserTitleInput as UpdateUserTitleInput, user_UpdateUserTitleInputSchema as UpdateUserTitleInputSchema, type user_UserRole as UserRole, user_UserRoleSchema as UserRoleSchema, user_userDescription as userDescription, user_userImage as userImage, user_userName as userName, user_userNoteText as userNoteText, user_userTitleColor as userTitleColor, user_userTitleIsActive as userTitleIsActive, user_userTitleName as userTitleName };
}

declare const karmaPointQuantity: z.ZodNumber;
declare const karmaPointComment: z.ZodArray<z.ZodObject<{
    locale: z.ZodEnum<["en", "ru"]>;
    value: z.ZodString;
}, "strip", z.ZodTypeAny, {
    value: string;
    locale: "en" | "ru";
}, {
    value: string;
    locale: "en" | "ru";
}>, "many">;
type GetKarmaPointsInput = Infer<typeof GetKarmaPointsInputSchema>;
declare const GetKarmaPointsInputSchema: z.ZodObject<{
    pagination: z.ZodDefault<z.ZodObject<{
        page: z.ZodDefault<z.ZodNumber>;
        size: z.ZodDefault<z.ZodNumber>;
    }, "strip", z.ZodTypeAny, {
        page: number;
        size: number;
    }, {
        page?: number | undefined;
        size?: number | undefined;
    }>>;
    userId: z.ZodString;
}, "strip", z.ZodTypeAny, {
    pagination: {
        page: number;
        size: number;
    };
    userId: string;
}, {
    userId: string;
    pagination?: {
        page?: number | undefined;
        size?: number | undefined;
    } | undefined;
}>;
type GetKarmaPointsOutput = Infer<typeof GetKarmaPointsOutputSchema>;
declare const GetKarmaPointsOutputSchema: z.ZodArray<z.ZodObject<{
    id: z.ZodString;
    author: z.ZodObject<{
        id: z.ZodString;
        email: z.ZodString;
        name: z.ZodArray<z.ZodObject<{
            locale: z.ZodEnum<["en", "ru"]>;
            value: z.ZodString;
        }, "strip", z.ZodTypeAny, {
            value: string;
            locale: "en" | "ru";
        }, {
            value: string;
            locale: "en" | "ru";
        }>, "many">;
        image: z.ZodNullable<z.ZodString>;
    }, "strip", z.ZodTypeAny, {
        id: string;
        email: string;
        name: {
            value: string;
            locale: "en" | "ru";
        }[];
        image: string | null;
    }, {
        id: string;
        email: string;
        name: {
            value: string;
            locale: "en" | "ru";
        }[];
        image: string | null;
    }>;
    quantity: z.ZodNumber;
    comment: z.ZodArray<z.ZodObject<{
        locale: z.ZodEnum<["en", "ru"]>;
        value: z.ZodString;
    }, "strip", z.ZodTypeAny, {
        value: string;
        locale: "en" | "ru";
    }, {
        value: string;
        locale: "en" | "ru";
    }>, "many">;
}, "strip", z.ZodTypeAny, {
    id: string;
    author: {
        id: string;
        email: string;
        name: {
            value: string;
            locale: "en" | "ru";
        }[];
        image: string | null;
    };
    comment: {
        value: string;
        locale: "en" | "ru";
    }[];
    quantity: number;
}, {
    id: string;
    author: {
        id: string;
        email: string;
        name: {
            value: string;
            locale: "en" | "ru";
        }[];
        image: string | null;
    };
    comment: {
        value: string;
        locale: "en" | "ru";
    }[];
    quantity: number;
}>, "many">;
type SpendKarmaPointInput = Infer<typeof SpendKarmaPointInputSchema>;
declare const SpendKarmaPointInputSchema: z.ZodObject<{
    sourceUserId: z.ZodString;
    targetUserId: z.ZodString;
    quantity: z.ZodNumber;
    comment: z.ZodArray<z.ZodObject<{
        locale: z.ZodEnum<["en", "ru"]>;
        value: z.ZodString;
    }, "strip", z.ZodTypeAny, {
        value: string;
        locale: "en" | "ru";
    }, {
        value: string;
        locale: "en" | "ru";
    }>, "many">;
}, "strip", z.ZodTypeAny, {
    comment: {
        value: string;
        locale: "en" | "ru";
    }[];
    quantity: number;
    sourceUserId: string;
    targetUserId: string;
}, {
    comment: {
        value: string;
        locale: "en" | "ru";
    }[];
    quantity: number;
    sourceUserId: string;
    targetUserId: string;
}>;
declare const userFeedbackValue: z.ZodNumber;
declare const userFeedbackText: z.ZodArray<z.ZodObject<{
    locale: z.ZodEnum<["en", "ru"]>;
    value: z.ZodString;
}, "strip", z.ZodTypeAny, {
    value: string;
    locale: "en" | "ru";
}, {
    value: string;
    locale: "en" | "ru";
}>, "many">;
type GetUserFeedbacksInput = Infer<typeof GetUserFeedbacksInputSchema>;
declare const GetUserFeedbacksInputSchema: z.ZodObject<{
    pagination: z.ZodDefault<z.ZodObject<{
        page: z.ZodDefault<z.ZodNumber>;
        size: z.ZodDefault<z.ZodNumber>;
    }, "strip", z.ZodTypeAny, {
        page: number;
        size: number;
    }, {
        page?: number | undefined;
        size?: number | undefined;
    }>>;
    userId: z.ZodString;
}, "strip", z.ZodTypeAny, {
    pagination: {
        page: number;
        size: number;
    };
    userId: string;
}, {
    userId: string;
    pagination?: {
        page?: number | undefined;
        size?: number | undefined;
    } | undefined;
}>;
type GetUserFeedbacksOutput = Infer<typeof GetUserFeedbacksOutputSchema>;
declare const GetUserFeedbacksOutputSchema: z.ZodArray<z.ZodObject<{
    id: z.ZodString;
    author: z.ZodNullable<z.ZodObject<{
        id: z.ZodString;
        email: z.ZodString;
        name: z.ZodArray<z.ZodObject<{
            locale: z.ZodEnum<["en", "ru"]>;
            value: z.ZodString;
        }, "strip", z.ZodTypeAny, {
            value: string;
            locale: "en" | "ru";
        }, {
            value: string;
            locale: "en" | "ru";
        }>, "many">;
        image: z.ZodNullable<z.ZodString>;
    }, "strip", z.ZodTypeAny, {
        id: string;
        email: string;
        name: {
            value: string;
            locale: "en" | "ru";
        }[];
        image: string | null;
    }, {
        id: string;
        email: string;
        name: {
            value: string;
            locale: "en" | "ru";
        }[];
        image: string | null;
    }>>;
    isAnonymous: z.ZodBoolean;
    value: z.ZodNumber;
    text: z.ZodArray<z.ZodObject<{
        locale: z.ZodEnum<["en", "ru"]>;
        value: z.ZodString;
    }, "strip", z.ZodTypeAny, {
        value: string;
        locale: "en" | "ru";
    }, {
        value: string;
        locale: "en" | "ru";
    }>, "many">;
}, "strip", z.ZodTypeAny, {
    value: number;
    id: string;
    text: {
        value: string;
        locale: "en" | "ru";
    }[];
    author: {
        id: string;
        email: string;
        name: {
            value: string;
            locale: "en" | "ru";
        }[];
        image: string | null;
    } | null;
    isAnonymous: boolean;
}, {
    value: number;
    id: string;
    text: {
        value: string;
        locale: "en" | "ru";
    }[];
    author: {
        id: string;
        email: string;
        name: {
            value: string;
            locale: "en" | "ru";
        }[];
        image: string | null;
    } | null;
    isAnonymous: boolean;
}>, "many">;
type CreateUserFeedbackInput = Infer<typeof CreateUserFeedbackInputSchema>;
declare const CreateUserFeedbackInputSchema: z.ZodObject<{
    sourceUserId: z.ZodString;
    targetUserId: z.ZodString;
    value: z.ZodNumber;
    isAnonymous: z.ZodBoolean;
    text: z.ZodArray<z.ZodObject<{
        locale: z.ZodEnum<["en", "ru"]>;
        value: z.ZodString;
    }, "strip", z.ZodTypeAny, {
        value: string;
        locale: "en" | "ru";
    }, {
        value: string;
        locale: "en" | "ru";
    }>, "many">;
}, "strip", z.ZodTypeAny, {
    value: number;
    text: {
        value: string;
        locale: "en" | "ru";
    }[];
    isAnonymous: boolean;
    sourceUserId: string;
    targetUserId: string;
}, {
    value: number;
    text: {
        value: string;
        locale: "en" | "ru";
    }[];
    isAnonymous: boolean;
    sourceUserId: string;
    targetUserId: string;
}>;
type GetUserSummaryInput = Infer<typeof GetUserSummaryInputSchema>;
declare const GetUserSummaryInputSchema: z.ZodObject<{
    userId: z.ZodString;
}, "strip", z.ZodTypeAny, {
    userId: string;
}, {
    userId: string;
}>;
type GetUserSummaryOutput = Infer<typeof GetUserSummaryOutputSchema>;
declare const GetUserSummaryOutputSchema: z.ZodObject<{
    rating: z.ZodNumber;
    karma: z.ZodNumber;
    rate: z.ZodNullable<z.ZodNumber>;
}, "strip", z.ZodTypeAny, {
    rating: number;
    karma: number;
    rate: number | null;
}, {
    rating: number;
    karma: number;
    rate: number | null;
}>;

type rating_CreateUserFeedbackInput = CreateUserFeedbackInput;
declare const rating_CreateUserFeedbackInputSchema: typeof CreateUserFeedbackInputSchema;
type rating_GetKarmaPointsInput = GetKarmaPointsInput;
declare const rating_GetKarmaPointsInputSchema: typeof GetKarmaPointsInputSchema;
type rating_GetKarmaPointsOutput = GetKarmaPointsOutput;
declare const rating_GetKarmaPointsOutputSchema: typeof GetKarmaPointsOutputSchema;
type rating_GetUserFeedbacksInput = GetUserFeedbacksInput;
declare const rating_GetUserFeedbacksInputSchema: typeof GetUserFeedbacksInputSchema;
type rating_GetUserFeedbacksOutput = GetUserFeedbacksOutput;
declare const rating_GetUserFeedbacksOutputSchema: typeof GetUserFeedbacksOutputSchema;
type rating_GetUserSummaryInput = GetUserSummaryInput;
declare const rating_GetUserSummaryInputSchema: typeof GetUserSummaryInputSchema;
type rating_GetUserSummaryOutput = GetUserSummaryOutput;
declare const rating_GetUserSummaryOutputSchema: typeof GetUserSummaryOutputSchema;
type rating_SpendKarmaPointInput = SpendKarmaPointInput;
declare const rating_SpendKarmaPointInputSchema: typeof SpendKarmaPointInputSchema;
declare const rating_karmaPointComment: typeof karmaPointComment;
declare const rating_karmaPointQuantity: typeof karmaPointQuantity;
declare const rating_userFeedbackText: typeof userFeedbackText;
declare const rating_userFeedbackValue: typeof userFeedbackValue;
declare namespace rating {
  export { type rating_CreateUserFeedbackInput as CreateUserFeedbackInput, rating_CreateUserFeedbackInputSchema as CreateUserFeedbackInputSchema, type rating_GetKarmaPointsInput as GetKarmaPointsInput, rating_GetKarmaPointsInputSchema as GetKarmaPointsInputSchema, type rating_GetKarmaPointsOutput as GetKarmaPointsOutput, rating_GetKarmaPointsOutputSchema as GetKarmaPointsOutputSchema, type rating_GetUserFeedbacksInput as GetUserFeedbacksInput, rating_GetUserFeedbacksInputSchema as GetUserFeedbacksInputSchema, type rating_GetUserFeedbacksOutput as GetUserFeedbacksOutput, rating_GetUserFeedbacksOutputSchema as GetUserFeedbacksOutputSchema, type rating_GetUserSummaryInput as GetUserSummaryInput, rating_GetUserSummaryInputSchema as GetUserSummaryInputSchema, type rating_GetUserSummaryOutput as GetUserSummaryOutput, rating_GetUserSummaryOutputSchema as GetUserSummaryOutputSchema, type rating_SpendKarmaPointInput as SpendKarmaPointInput, rating_SpendKarmaPointInputSchema as SpendKarmaPointInputSchema, rating_karmaPointComment as karmaPointComment, rating_karmaPointQuantity as karmaPointQuantity, rating_userFeedbackText as userFeedbackText, rating_userFeedbackValue as userFeedbackValue };
}

declare const tagName: z.ZodArray<z.ZodObject<{
    locale: z.ZodEnum<["en", "ru"]>;
    value: z.ZodString;
}, "strip", z.ZodTypeAny, {
    value: string;
    locale: "en" | "ru";
}, {
    value: string;
    locale: "en" | "ru";
}>, "many">;
type GetTagsInput = Infer<typeof GetTagsInputSchema>;
declare const GetTagsInputSchema: z.ZodObject<{
    pagination: z.ZodOptional<z.ZodDefault<z.ZodObject<{
        page: z.ZodDefault<z.ZodNumber>;
        size: z.ZodDefault<z.ZodNumber>;
    }, "strip", z.ZodTypeAny, {
        page: number;
        size: number;
    }, {
        page?: number | undefined;
        size?: number | undefined;
    }>>>;
    ids: z.ZodOptional<z.ZodArray<z.ZodString, "many">>;
    query: z.ZodOptional<z.ZodString>;
}, "strip", z.ZodTypeAny, {
    pagination?: {
        page: number;
        size: number;
    } | undefined;
    ids?: string[] | undefined;
    query?: string | undefined;
}, {
    pagination?: {
        page?: number | undefined;
        size?: number | undefined;
    } | undefined;
    ids?: string[] | undefined;
    query?: string | undefined;
}>;
type GetTagsOutput = Infer<typeof GetTagsOutputSchema>;
declare const GetTagsOutputSchema: z.ZodArray<z.ZodObject<{
    id: z.ZodString;
    name: z.ZodArray<z.ZodObject<{
        locale: z.ZodEnum<["en", "ru"]>;
        value: z.ZodString;
    }, "strip", z.ZodTypeAny, {
        value: string;
        locale: "en" | "ru";
    }, {
        value: string;
        locale: "en" | "ru";
    }>, "many">;
    deletedAt: z.ZodOptional<z.ZodNullable<z.ZodDate>>;
}, "strip", z.ZodTypeAny, {
    id: string;
    name: {
        value: string;
        locale: "en" | "ru";
    }[];
    deletedAt?: Date | null | undefined;
}, {
    id: string;
    name: {
        value: string;
        locale: "en" | "ru";
    }[];
    deletedAt?: Date | null | undefined;
}>, "many">;
type CreateTagInput = Infer<typeof CreateTagInputSchema>;
declare const CreateTagInputSchema: z.ZodObject<{
    name: z.ZodArray<z.ZodObject<{
        locale: z.ZodEnum<["en", "ru"]>;
        value: z.ZodString;
    }, "strip", z.ZodTypeAny, {
        value: string;
        locale: "en" | "ru";
    }, {
        value: string;
        locale: "en" | "ru";
    }>, "many">;
}, "strip", z.ZodTypeAny, {
    name: {
        value: string;
        locale: "en" | "ru";
    }[];
}, {
    name: {
        value: string;
        locale: "en" | "ru";
    }[];
}>;
type UpdateTagInput = Infer<typeof UpdateTagInputSchema>;
declare const UpdateTagInputSchema: z.ZodObject<{
    id: z.ZodString;
    name: z.ZodArray<z.ZodObject<{
        locale: z.ZodEnum<["en", "ru"]>;
        value: z.ZodString;
    }, "strip", z.ZodTypeAny, {
        value: string;
        locale: "en" | "ru";
    }, {
        value: string;
        locale: "en" | "ru";
    }>, "many">;
}, "strip", z.ZodTypeAny, {
    id: string;
    name: {
        value: string;
        locale: "en" | "ru";
    }[];
}, {
    id: string;
    name: {
        value: string;
        locale: "en" | "ru";
    }[];
}>;

type tag_CreateTagInput = CreateTagInput;
declare const tag_CreateTagInputSchema: typeof CreateTagInputSchema;
type tag_GetTagsInput = GetTagsInput;
declare const tag_GetTagsInputSchema: typeof GetTagsInputSchema;
type tag_GetTagsOutput = GetTagsOutput;
declare const tag_GetTagsOutputSchema: typeof GetTagsOutputSchema;
type tag_UpdateTagInput = UpdateTagInput;
declare const tag_UpdateTagInputSchema: typeof UpdateTagInputSchema;
declare const tag_tagName: typeof tagName;
declare namespace tag {
  export { type tag_CreateTagInput as CreateTagInput, tag_CreateTagInputSchema as CreateTagInputSchema, type tag_GetTagsInput as GetTagsInput, tag_GetTagsInputSchema as GetTagsInputSchema, type tag_GetTagsOutput as GetTagsOutput, tag_GetTagsOutputSchema as GetTagsOutputSchema, type tag_UpdateTagInput as UpdateTagInput, tag_UpdateTagInputSchema as UpdateTagInputSchema, tag_tagName as tagName };
}

export { auth as Auth, common as Common, commune as Commune, consts as Consts, rating as Rating, reactor as Reactor, tag as Tag, user as User };
