import { ConfigService } from "@nestjs/config";
export interface FileInfo {
    originalname: string;
    buffer: Buffer;
    mimetype: string;
    size: number;
}
export declare const minioImageEntities: readonly ["commune", "user", "post", "reactor-hub", "reactor-community"];
export type MinioImageEntity = (typeof minioImageEntities)[number];
export declare class MinioService {
    private readonly configService;
    private readonly client;
    private readonly logger;
    private readonly buckets;
    constructor(configService: ConfigService);
    private initializeBuckets;
    getImageObjectName(entityId: string, index?: number): string;
    uploadImage(file: FileInfo, entity: MinioImageEntity, entityId: string, index?: number): Promise<string>;
    uploadFile(file: FileInfo, bucket: string, objectName: string): Promise<string>;
    deleteImage(entity: MinioImageEntity, entityId: string, index?: number): Promise<void>;
    deleteFile(bucket: string, objectName: string): Promise<void>;
}
