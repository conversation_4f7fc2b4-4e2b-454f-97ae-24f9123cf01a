import * as express_serve_static_core from 'express-serve-static-core';
import { Request, Response } from 'express';
import { D as DeepPartial } from '../types.d-BnyDggQq.js';
import { SchemaRoute, SchemaEndpoint, Schema, Transformer } from './core.js';
import { z } from 'zod';
import '@ocelotjungle/case-converters';

type MaybePromise<T> = T | Promise<T>;
type SchemaRouteHandlers<TSchemaEndpoint extends SchemaRoute, TMetadata = never> = {
    [M in keyof TSchemaEndpoint]: TSchemaEndpoint[M] extends SchemaEndpoint ? (input: TSchemaEndpoint[M]["input"] extends z.ZodType ? z.infer<TSchemaEndpoint[M]["input"]> : TSchemaEndpoint[M]["input"] extends null ? never : unknown, metadata: (TSchemaEndpoint[M]["isMetadataUsed"] extends false ? never : TMetadata) | (TSchemaEndpoint[M]["isMetadataRequired"] extends false ? null : never), rest: {
        req: Request;
        res: Response;
    }) => MaybePromise<TSchemaEndpoint[M]["output"] extends z.ZodType ? z.infer<TSchemaEndpoint[M]["output"]> : unknown> : TSchemaEndpoint[M] extends SchemaRoute ? SchemaRouteHandlers<TSchemaEndpoint[M], TMetadata> : never;
};
type Handlers<TSchema extends Schema, TMetadata = never> = {
    [K in keyof TSchema]: TSchema[K] extends SchemaRoute ? SchemaRouteHandlers<TSchema[K], TMetadata> : TSchema[K] extends Schema ? Handlers<TSchema[K], TMetadata> : never;
};
type PartialHandlers<TSchema extends Schema, TMetadata = never> = DeepPartial<Handlers<TSchema, TMetadata>>;
declare function createServer<TSchema extends Schema, TMetadata extends Record<string, any> = never>(schema: TSchema, handlers: PartialHandlers<TSchema, TMetadata>, options?: {
    transformer?: Transformer;
    getMetadata?: (req: Request) => TMetadata | null;
}): {
    router: express_serve_static_core.Router;
    register: (handlers: PartialHandlers<TSchema, TMetadata>) => void;
};

export { type Handlers, type PartialHandlers, type SchemaRouteHandlers, createServer };
