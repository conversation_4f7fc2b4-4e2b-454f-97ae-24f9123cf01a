{"version": 3, "sources": ["c:\\nodejs\\projects\\commune\\commune\\libs\\api\\dist\\acrpc\\schema.cjs"], "names": [], "mappings": "AAAA;AACE;AACA;AACA;AACA;AACA;AACA;AACA;AACF,yDAA8B;AAC9B;AACE;AACF,yDAA8B;AAC9B,iCAA8B;AAC9B;AACA;AACA,IAAI,0BAA0B,EAAE,UAAU;AAC1C,IAAI,mBAAmB,EAAE,UAAU;AACnC,IAAI,wBAAwB,EAAE,UAAU;AACxC,IAAI,sBAAsB,EAAE,yBAAyB;AACrD,IAAI,OAAO,EAAE;AACb,EAAE,IAAI,EAAE;AACR,IAAI,GAAG,EAAE;AACT,MAAM,IAAI,EAAE;AACZ,QAAQ,KAAK,EAAE,8BAAY,CAAC,kBAAkB;AAC9C,QAAQ,MAAM,EAAE,8BAAY,CAAC,mBAAmB;AAChD,QAAQ,cAAc,EAAE;AACxB,MAAM;AACN,IAAI,CAAC;AACL,IAAI,MAAM,EAAE;AACZ,MAAM,IAAI,EAAE;AACZ,QAAQ,KAAK,EAAE,8BAAY,CAAC,iBAAiB;AAC7C,QAAQ,MAAM,EAAE,8BAAY,CAAC,sBAAsB;AACnD,QAAQ,cAAc,EAAE,KAAK;AAC7B,QAAQ,UAAU,EAAE,CAAC,UAAU;AAC/B,MAAM;AACN,IAAI,CAAC;AACL,IAAI,MAAM,EAAE;AACZ,MAAM,IAAI,EAAE;AACZ,QAAQ,KAAK,EAAE,8BAAY,CAAC,iBAAiB;AAC7C,QAAQ,MAAM,EAAE,8BAAY,CAAC,sBAAsB;AACnD,QAAQ,cAAc,EAAE,KAAK;AAC7B,QAAQ,UAAU,EAAE,CAAC,UAAU;AAC/B,MAAM;AACN,IAAI,CAAC;AACL,IAAI,OAAO,EAAE;AACb,MAAM,GAAG,EAAE;AACX,QAAQ,KAAK,EAAE,IAAI;AACnB,QAAQ,MAAM,EAAE,IAAI;AACpB,QAAQ,cAAc,EAAE,KAAK;AAC7B,QAAQ,UAAU,EAAE,CAAC,UAAU;AAC/B,MAAM;AACN,IAAI;AACJ,EAAE,CAAC;AACH,EAAE,OAAO,EAAE;AACX,IAAI,kBAAkB,EAAE;AACxB,MAAM,IAAI,EAAE;AACZ,QAAQ,KAAK,EAAE,iCAAe,CAAC,6BAA6B;AAC5D,QAAQ,MAAM,EAAE,IAAI;AACpB,QAAQ,0BAA0B,EAAE;AACpC,MAAM;AACN,IAAI,CAAC;AACL,IAAI,IAAI,EAAE;AACV,MAAM,GAAG,EAAE;AACX,QAAQ,KAAK,EAAE,iCAAe,CAAC,sBAAsB;AACrD,QAAQ,MAAM,EAAE,iCAAe,CAAC,uBAAuB;AACvD,QAAQ,YAAY,EAAE;AACtB,MAAM;AACN,IAAI,CAAC;AACL,IAAI,GAAG,EAAE;AACT,MAAM,KAAK,EAAE,gCAAc,CAAC,kBAAkB;AAC9C,MAAM,MAAM,EAAE,iCAAe,CAAC,sBAAsB;AACpD,MAAM,YAAY,EAAE;AACpB,IAAI,CAAC;AACL,IAAI,IAAI,EAAE;AACV,MAAM,KAAK,EAAE,iCAAe,CAAC,wBAAwB;AACrD,MAAM,MAAM,EAAE,gCAAc,CAAC,kBAAkB;AAC/C,MAAM,0BAA0B,EAAE;AAClC,IAAI,CAAC;AACL,IAAI,KAAK,EAAE;AACX,MAAM,KAAK,EAAE,iCAAe,CAAC,wBAAwB;AACrD,MAAM,MAAM,EAAE,IAAI;AAClB,MAAM,0BAA0B,EAAE;AAClC,IAAI,CAAC;AACL,IAAI,MAAM,EAAE;AACZ,MAAM,KAAK,EAAE,gCAAc,CAAC,kBAAkB;AAC9C,MAAM,MAAM,EAAE,IAAI;AAClB,MAAM,0BAA0B,EAAE;AAClC,IAAI,CAAC;AACL,IAAI,MAAM,EAAE;AACZ,MAAM,IAAI,EAAE;AACZ,QAAQ,GAAG,EAAE;AACb,UAAU,KAAK,EAAE,iCAAe,CAAC,4BAA4B;AAC7D,UAAU,MAAM,EAAE,iCAAe,CAAC,6BAA6B;AAC/D,UAAU,YAAY,EAAE;AACxB,QAAQ;AACR,MAAM,CAAC;AACP,MAAM,IAAI,EAAE;AACZ,QAAQ,KAAK,EAAE,iCAAe,CAAC,8BAA8B;AAC7D,QAAQ,MAAM,EAAE,gCAAc,CAAC,kBAAkB;AACjD,QAAQ,0BAA0B,EAAE;AACpC,MAAM,CAAC;AACP,MAAM,MAAM,EAAE;AACd,QAAQ,KAAK,EAAE,gCAAc,CAAC,kBAAkB;AAChD,QAAQ,MAAM,EAAE,IAAI;AACpB,QAAQ,0BAA0B,EAAE;AACpC,MAAM;AACN,IAAI,CAAC;AACL,IAAI,UAAU,EAAE;AAChB,MAAM,IAAI,EAAE;AACZ,QAAQ,GAAG,EAAE;AACb,UAAU,KAAK,EAAE,iCAAe,CAAC,gCAAgC;AACjE,UAAU,MAAM,EAAE,iCAAe,CAAC,iCAAiC;AACnE,UAAU,YAAY,EAAE;AACxB,QAAQ;AACR,MAAM,CAAC;AACP,MAAM,IAAI,EAAE;AACZ,QAAQ,KAAK,EAAE,iCAAe,CAAC,kCAAkC;AACjE,QAAQ,MAAM,EAAE,gCAAc,CAAC,kBAAkB;AACjD,QAAQ,0BAA0B,EAAE;AACpC,MAAM,CAAC;AACP,MAAM,MAAM,EAAE;AACd,QAAQ,KAAK,EAAE,gCAAc,CAAC,kBAAkB;AAChD,QAAQ,MAAM,EAAE,IAAI;AACpB,QAAQ,0BAA0B,EAAE;AACpC,MAAM,CAAC;AACP,MAAM,MAAM,EAAE;AACd,QAAQ,IAAI,EAAE;AACd,UAAU,KAAK,EAAE,gCAAc,CAAC,kBAAkB;AAClD,UAAU,MAAM,EAAE,IAAI;AACtB;AACA,UAAU,UAAU,EAAE,CAAC,UAAU;AACjC,QAAQ;AACR,MAAM,CAAC;AACP,MAAM,MAAM,EAAE;AACd,QAAQ,IAAI,EAAE;AACd,UAAU,KAAK,EAAE,gCAAc,CAAC,kBAAkB;AAClD,UAAU,MAAM,EAAE,IAAI;AACtB;AACA,UAAU,UAAU,EAAE,CAAC,UAAU;AACjC,QAAQ;AACR,MAAM;AACN,IAAI,CAAC;AACL,IAAI,WAAW,EAAE;AACjB,MAAM,IAAI,EAAE;AACZ,QAAQ,GAAG,EAAE;AACb,UAAU,KAAK,EAAE,iCAAe,CAAC,iCAAiC;AAClE,UAAU,MAAM,EAAE,iCAAe,CAAC,kCAAkC;AACpE,UAAU,YAAY,EAAE;AACxB,QAAQ;AACR,MAAM,CAAC;AACP,MAAM,IAAI,EAAE;AACZ,QAAQ,KAAK,EAAE,iCAAe,CAAC,mCAAmC;AAClE,QAAQ,MAAM,EAAE,gCAAc,CAAC,kBAAkB;AACjD,QAAQ,0BAA0B,EAAE;AACpC,MAAM,CAAC;AACP,MAAM,MAAM,EAAE;AACd,QAAQ,KAAK,EAAE,gCAAc,CAAC,kBAAkB;AAChD,QAAQ,MAAM,EAAE,IAAI;AACpB,QAAQ,0BAA0B,EAAE;AACpC,MAAM,CAAC;AACP,MAAM,MAAM,EAAE;AACd,QAAQ,IAAI,EAAE;AACd,UAAU,KAAK,EAAE,gCAAc,CAAC,kBAAkB;AAClD,UAAU,MAAM,EAAE,IAAI;AACtB;AACA,UAAU,UAAU,EAAE,CAAC,UAAU;AACjC,QAAQ;AACR,MAAM,CAAC;AACP,MAAM,MAAM,EAAE;AACd,QAAQ,IAAI,EAAE;AACd,UAAU,KAAK,EAAE,gCAAc,CAAC,kBAAkB;AAClD,UAAU,MAAM,EAAE,IAAI;AACtB;AACA,UAAU,UAAU,EAAE,CAAC,UAAU;AACjC,QAAQ;AACR,MAAM;AACN,IAAI;AACJ,EAAE,CAAC;AACH,EAAE,MAAM,EAAE;AACV,IAAI,KAAK,EAAE;AACX,MAAM,IAAI,EAAE;AACZ,QAAQ,GAAG,EAAE;AACb,UAAU,KAAK,EAAE,gCAAc,CAAC,yBAAyB;AACzD,UAAU,MAAM,EAAE,gCAAc,CAAC,0BAA0B;AAC3D,UAAU,YAAY,EAAE;AACxB,QAAQ;AACR,MAAM,CAAC;AACP,MAAM,IAAI,EAAE;AACZ,QAAQ,KAAK,EAAE,gCAAc,CAAC,0BAA0B;AACxD,QAAQ,MAAM,EAAE,gCAAc,CAAC,kBAAkB;AACjD,QAAQ,0BAA0B,EAAE,CAAC;AACrC,QAAQ,UAAU,EAAE,CAAC,iBAAiB;AACtC,MAAM;AACN,IAAI,CAAC;AACL,IAAI,QAAQ,EAAE;AACd,MAAM,IAAI,EAAE;AACZ,QAAQ,GAAG,EAAE;AACb,UAAU,KAAK,EAAE,gCAAc,CAAC,2BAA2B;AAC3D,UAAU,MAAM,EAAE,gCAAc,CAAC,4BAA4B;AAC7D,UAAU,YAAY,EAAE;AACxB,QAAQ;AACR,MAAM,CAAC;AACP,MAAM,IAAI,EAAE;AACZ,QAAQ,KAAK,EAAE,gCAAc,CAAC,6BAA6B;AAC3D,QAAQ,MAAM,EAAE,gCAAc,CAAC,kBAAkB;AACjD,QAAQ,0BAA0B,EAAE,CAAC;AACrC,QAAQ,UAAU,EAAE,CAAC,iBAAiB;AACtC,MAAM;AACN,IAAI,CAAC;AACL,IAAI,OAAO,EAAE;AACb,MAAM,GAAG,EAAE;AACX,QAAQ,KAAK,EAAE,gCAAc,CAAC,yBAAyB;AACvD,QAAQ,MAAM,EAAE,gCAAc,CAAC,0BAA0B;AACzD,QAAQ,YAAY,EAAE;AACtB,MAAM;AACN,IAAI;AACJ,EAAE,CAAC;AACH,EAAE,OAAO,EAAE;AACX,IAAI,IAAI,EAAE;AACV,MAAM,IAAI,EAAE;AACZ,QAAQ,GAAG,EAAE;AACb,UAAU,KAAK,EAAE,iCAAe,CAAC,mBAAmB;AACpD,UAAU,MAAM,EAAE,iCAAe,CAAC,oBAAoB;AACtD,UAAU,YAAY,EAAE;AACxB,QAAQ;AACR,MAAM,CAAC;AACP,MAAM,IAAI,EAAE;AACZ,QAAQ,KAAK,EAAE,iCAAe,CAAC,qBAAqB;AACpD,QAAQ,MAAM,EAAE,gCAAc,CAAC,kBAAkB;AACjD,QAAQ,0BAA0B,EAAE;AACpC,MAAM,CAAC;AACP,MAAM,KAAK,EAAE;AACb,QAAQ,KAAK,EAAE,iCAAe,CAAC,qBAAqB;AACpD,QAAQ,MAAM,EAAE,IAAI;AACpB,QAAQ,0BAA0B,EAAE;AACpC,MAAM,CAAC;AACP,MAAM,MAAM,EAAE;AACd,QAAQ,KAAK,EAAE,iCAAe,CAAC,qBAAqB;AACpD,QAAQ,MAAM,EAAE,IAAI;AACpB,QAAQ,0BAA0B,EAAE;AACpC,MAAM,CAAC;AACP,MAAM,MAAM,EAAE;AACd,QAAQ,IAAI,EAAE;AACd,UAAU,KAAK,EAAE,iCAAe,CAAC,2BAA2B;AAC5D,UAAU,MAAM,EAAE,iCAAe,CAAC,4BAA4B;AAC9D,UAAU,0BAA0B,EAAE;AACtC,QAAQ;AACR,MAAM,CAAC;AACP,MAAM,UAAU,EAAE;AAClB,QAAQ,IAAI,EAAE;AACd,UAAU,KAAK,EAAE,iCAAe,CAAC,+BAA+B;AAChE,UAAU,MAAM,EAAE,iCAAe,CAAC,gCAAgC;AAClE,UAAU,0BAA0B,EAAE;AACtC,QAAQ;AACR,MAAM;AACN,IAAI,CAAC;AACL,IAAI,OAAO,EAAE;AACb,MAAM,IAAI,EAAE;AACZ,QAAQ,GAAG,EAAE;AACb,UAAU,KAAK,EAAE,iCAAe,CAAC,sBAAsB;AACvD,UAAU,MAAM,EAAE,iCAAe,CAAC,uBAAuB;AACzD,UAAU,YAAY,EAAE;AACxB,QAAQ;AACR,MAAM,CAAC;AACP,MAAM,IAAI,EAAE;AACZ,QAAQ,KAAK,EAAE,iCAAe,CAAC,wBAAwB;AACvD,QAAQ,MAAM,EAAE,gCAAc,CAAC,kBAAkB;AACjD,QAAQ,0BAA0B,EAAE;AACpC,MAAM,CAAC;AACP,MAAM,KAAK,EAAE;AACb,QAAQ,KAAK,EAAE,iCAAe,CAAC,wBAAwB;AACvD,QAAQ,MAAM,EAAE,IAAI;AACpB,QAAQ,0BAA0B,EAAE;AACpC,MAAM,CAAC;AACP,MAAM,MAAM,EAAE;AACd,QAAQ,KAAK,EAAE,iCAAe,CAAC,wBAAwB;AACvD,QAAQ,MAAM,EAAE,IAAI;AACpB,QAAQ,0BAA0B,EAAE;AACpC,MAAM,CAAC;AACP,MAAM,MAAM,EAAE;AACd,QAAQ,IAAI,EAAE;AACd,UAAU,KAAK,EAAE,iCAAe,CAAC,8BAA8B;AAC/D,UAAU,MAAM,EAAE,iCAAe,CAAC,+BAA+B;AACjE,UAAU,0BAA0B,EAAE;AACtC,QAAQ;AACR,MAAM,CAAC;AACP,MAAM,SAAS,EAAE;AACjB,QAAQ,IAAI,EAAE;AACd,UAAU,KAAK,EAAE,iCAAe,CAAC,2BAA2B;AAC5D,UAAU,MAAM,EAAE,IAAI;AACtB,UAAU,0BAA0B,EAAE;AACtC,QAAQ;AACR,MAAM;AACN,IAAI,CAAC;AACL,IAAI,IAAI,EAAE;AACV,MAAM,IAAI,EAAE;AACZ,QAAQ,GAAG,EAAE;AACb,UAAU,KAAK,EAAE,IAAI;AACrB,UAAU,MAAM,EAAE,IAAI;AACtB,UAAU,YAAY,EAAE;AACxB,QAAQ;AACR,MAAM,CAAC;AACP,MAAM,IAAI,EAAE;AACZ,QAAQ,KAAK,EAAE,iCAAe,CAAC,qBAAqB;AACpD,QAAQ,MAAM,EAAE,gCAAc,CAAC,kBAAkB;AACjD,QAAQ,0BAA0B,EAAE;AACpC,MAAM,CAAC;AACP,MAAM,KAAK,EAAE;AACb,QAAQ,KAAK,EAAE,iCAAe,CAAC,qBAAqB;AACpD,QAAQ,MAAM,EAAE,IAAI;AACpB,QAAQ,0BAA0B,EAAE;AACpC,MAAM,CAAC;AACP,MAAM,MAAM,EAAE;AACd,QAAQ,KAAK,EAAE,gCAAc,CAAC,kBAAkB;AAChD,QAAQ,MAAM,EAAE,IAAI;AACpB,QAAQ,0BAA0B,EAAE;AACpC,MAAM;AACN,IAAI,CAAC;AACL,IAAI,GAAG,EAAE;AACT,MAAM,IAAI,EAAE;AACZ,QAAQ,GAAG,EAAE;AACb,UAAU,KAAK,EAAE,iCAAe,CAAC,kBAAkB;AACnD,UAAU,MAAM,EAAE,iCAAe,CAAC,mBAAmB;AACrD,UAAU,YAAY,EAAE;AACxB,QAAQ;AACR,MAAM,CAAC;AACP,MAAM,IAAI,EAAE;AACZ,QAAQ,KAAK,EAAE,iCAAe,CAAC,oBAAoB;AACnD,QAAQ,MAAM,EAAE,gCAAc,CAAC,kBAAkB;AACjD,QAAQ,0BAA0B,EAAE;AACpC,MAAM,CAAC;AACP,MAAM,KAAK,EAAE;AACb,QAAQ,KAAK,EAAE,iCAAe,CAAC,oBAAoB;AACnD,QAAQ,MAAM,EAAE,IAAI;AACpB,QAAQ,0BAA0B,EAAE;AACpC,MAAM;AACN;AACA;AACA;AACA;AACA;AACA,IAAI,CAAC;AACL,IAAI,SAAS,EAAE;AACf,MAAM,IAAI,EAAE;AACZ,QAAQ,GAAG,EAAE;AACb,UAAU,KAAK,EAAE,iCAAe,CAAC,yBAAyB;AAC1D,UAAU,MAAM,EAAE,iCAAe,CAAC,0BAA0B;AAC5D,UAAU,YAAY,EAAE;AACxB,QAAQ;AACR,MAAM,CAAC;AACP,MAAM,IAAI,EAAE;AACZ,QAAQ,KAAK,EAAE,iCAAe,CAAC,0BAA0B;AACzD,QAAQ,MAAM,EAAE,gCAAc,CAAC,kBAAkB;AACjD,QAAQ,0BAA0B,EAAE;AACpC,MAAM,CAAC;AACP,MAAM,KAAK,EAAE;AACb,QAAQ,KAAK,EAAE,iCAAe,CAAC,0BAA0B;AACzD,QAAQ,MAAM,EAAE,IAAI;AACpB,QAAQ,0BAA0B,EAAE;AACpC,MAAM;AACN;AACA;AACA;AACA;AACA;AACA,IAAI;AACJ,EAAE,CAAC;AACH,EAAE,GAAG,EAAE;AACP,IAAI,IAAI,EAAE;AACV,MAAM,GAAG,EAAE;AACX,QAAQ,KAAK,EAAE,6BAAW,CAAC,kBAAkB;AAC7C,QAAQ,MAAM,EAAE,6BAAW,CAAC,mBAAmB;AAC/C,QAAQ,YAAY,EAAE;AACtB,MAAM;AACN,IAAI,CAAC;AACL,IAAI,IAAI,EAAE;AACV,MAAM,KAAK,EAAE,6BAAW,CAAC,oBAAoB;AAC7C,MAAM,MAAM,EAAE,gCAAc,CAAC,kBAAkB;AAC/C,MAAM,0BAA0B,EAAE;AAClC,IAAI,CAAC;AACL,IAAI,KAAK,EAAE;AACX,MAAM,KAAK,EAAE,6BAAW,CAAC,oBAAoB;AAC7C,MAAM,MAAM,EAAE,IAAI;AAClB,MAAM,0BAA0B,EAAE;AAClC,IAAI,CAAC;AACL,IAAI,MAAM,EAAE;AACZ,MAAM,KAAK,EAAE,gCAAc,CAAC,kBAAkB;AAC9C,MAAM,MAAM,EAAE,IAAI;AAClB,MAAM,0BAA0B,EAAE;AAClC,IAAI;AACJ,EAAE,CAAC;AACH,EAAE,IAAI,EAAE;AACR,IAAI,IAAI,EAAE;AACV,MAAM,GAAG,EAAE;AACX,QAAQ,KAAK,EAAE,8BAAY,CAAC,mBAAmB;AAC/C,QAAQ,MAAM,EAAE,8BAAY,CAAC,oBAAoB;AACjD,QAAQ,YAAY,EAAE;AACtB,MAAM;AACN,IAAI,CAAC;AACL,IAAI,EAAE,EAAE;AACR,MAAM,GAAG,EAAE;AACX,QAAQ,KAAK,EAAE,IAAI;AACnB,QAAQ,MAAM,EAAE,8BAAY,CAAC,iBAAiB;AAC9C,QAAQ,YAAY,EAAE;AACtB,MAAM;AACN,IAAI,CAAC;AACL,IAAI,KAAK,EAAE;AACX,MAAM,KAAK,EAAE,8BAAY,CAAC,qBAAqB;AAC/C,MAAM,MAAM,EAAE,IAAI;AAClB,MAAM,0BAA0B,EAAE;AAClC,IAAI,CAAC;AACL,IAAI,KAAK,EAAE;AACX,MAAM,IAAI,EAAE;AACZ,QAAQ,GAAG,EAAE;AACb,UAAU,KAAK,EAAE,8BAAY,CAAC,wBAAwB;AACtD,UAAU,MAAM,EAAE,8BAAY,CAAC,yBAAyB;AACxD,UAAU,YAAY,EAAE;AACxB,QAAQ;AACR,MAAM,CAAC;AACP,MAAM,IAAI,EAAE;AACZ,QAAQ,KAAK,EAAE,8BAAY,CAAC,0BAA0B;AACtD,QAAQ,MAAM,EAAE,gCAAc,CAAC,kBAAkB;AACjD,QAAQ,0BAA0B,EAAE;AACpC,MAAM,CAAC;AACP,MAAM,KAAK,EAAE;AACb,QAAQ,KAAK,EAAE,8BAAY,CAAC,0BAA0B;AACtD,QAAQ,MAAM,EAAE,IAAI;AACpB,QAAQ,0BAA0B,EAAE;AACpC,MAAM,CAAC;AACP,MAAM,MAAM,EAAE;AACd,QAAQ,KAAK,EAAE,gCAAc,CAAC,kBAAkB;AAChD,QAAQ,MAAM,EAAE,IAAI;AACpB,QAAQ,0BAA0B,EAAE;AACpC,MAAM;AACN,IAAI,CAAC;AACL,IAAI,IAAI,EAAE;AACV,MAAM,GAAG,EAAE;AACX,QAAQ,KAAK,EAAE,8BAAY,CAAC,sBAAsB;AAClD,QAAQ,MAAM,EAAE,8BAAY,CAAC,uBAAuB;AACpD,QAAQ,YAAY,EAAE;AACtB,MAAM,CAAC;AACP,MAAM,GAAG,EAAE;AACX,QAAQ,KAAK,EAAE,8BAAY,CAAC,yBAAyB;AACrD,QAAQ,MAAM,EAAE,IAAI;AACpB,QAAQ,0BAA0B,EAAE;AACpC,MAAM;AACN,IAAI;AACJ,EAAE;AACF,CAAC;AACD,IAAI,YAAY,EAAE,sCAAoB;AACtC;AACE;AACA;AACA;AACA;AACA;AACA;AACF,6RAAC", "file": "C:\\nodejs\\projects\\commune\\commune\\libs\\api\\dist\\acrpc\\schema.cjs", "sourcesContent": [null]}