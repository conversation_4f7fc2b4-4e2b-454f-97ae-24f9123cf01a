<script lang="ts">
  import type { Common } from "@commune/api";
  import type { CommentEntity } from "./comment-tree";

  import { getClient } from "$lib/acrpc";
  import PostCard from "../post-card.svelte";
  import RightMenu from "../right-menu.svelte";
  import Comment from "./comment.svelte";
  import { CommentTree } from "./comment-tree";
  import LocalizedTextarea from "../../(index)/components/localized-textarea.svelte";

  const i18n = {
    en: {
      reactor: "Reactor",
      comments: "Comments",
      commentPlaceholder: "Write your comment...",
      submit: "Submit",
    },
    ru: {
      reactor: "Реактор",
      comments: "Комментарии",
      commentPlaceholder: "Напишите ваш комментарий...",
      submit: "Отправить",
    },
  };

  const { fetcher: api } = getClient();

  const { data } = $props();
  const { locale, routeLocale, toLocaleHref, getAppropriateLocalization } = $derived(data);

  const t = $derived(i18n[locale]);

  let post = $state(data.post);

  const title = $derived(getAppropriateLocalization(post.title));

  let comments = $state<CommentEntity[]>(data.comments);
  const commentTree = $derived(new CommentTree(comments));

  let commentText = $state<Common.Localizations>([]);

  async function addComment(id: string) {
    const [comment] = await api.reactor.comment.list.get({ id });

    if (!comment) {
      throw new Error("Comment not found");
    }

    comments = [
      ...comments,
      {
        ...comment,
        isMustBeTop: true,
      },
    ];

    const parentPath = commentTree.getParentPath(comment.path);

    if (parentPath) {
      commentTree.incrementChildrenCount(parentPath);
    }
  }

  async function submitComment() {
    const { id } = await api.reactor.comment.post({
      entityType: "post",
      entityId: post.id,
      body: commentText,
    });

    commentText = [];

    addComment(id);
  }
</script>

<svelte:head>
  <title>{title} — {t.reactor}</title>
</svelte:head>

<div class="row g-4 mt-3">
  <div class="col-3"></div>

  <!-- Main Content (2-9 columns) -->
  <div class="col-6">
    <div class="post-detail">
      <PostCard {locale} {post} {toLocaleHref} {getAppropriateLocalization} />

      <!-- Comments Section -->
      <div class="comments-section mt-4">
        <h4 class="mb-3">{t.comments} ({comments.length})</h4>

        <div class="comments-list">
          {#if commentTree}
            {#each commentTree.getRootComments() as comment (comment.id)}
              <Comment
                {comment}
                {locale}
                {routeLocale}
                expanded={false}
                {commentTree}
                {addComment}
                {getAppropriateLocalization}
              />
            {/each}
          {/if}

          <!-- {#each comments as comment (comment.id)}
            <Comment {comment} {locale} expanded={false} />
          {/each} -->
        </div>
      </div>

      <!-- Post Comment Form -->
      <div class="post-comment-form mt-4">
        <LocalizedTextarea
          {locale}
          id="post-comment"
          label=""
          placeholder={t.commentPlaceholder}
          rows={3}
          bind:value={commentText}
          languageSelectPosition="bottom"
        >
          <button class="btn btn-success btn-sm" onclick={submitComment}>
            <i class="bi bi-send me-1"></i>
            {t.submit}
          </button>
        </LocalizedTextarea>
      </div>
    </div>
  </div>

  <!-- Right Menu (10-11 columns) -->
  <div class="col-2">
    <RightMenu {locale} {toLocaleHref} />
  </div>
</div>

<style>
  .comments-section {
    background-color: #f8f9fa;
    border-radius: 0.5rem;
    padding: 1.5rem;
  }
</style>
