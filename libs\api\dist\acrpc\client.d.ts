import { Schema<PERSON>out<PERSON>, SchemaEndpoint, Schema, Transformer, Method } from './core.js';
import { z } from 'zod';
import '@ocelotjungle/case-converters';

type MaybePromise<T> = T | Promise<T>;
type ClientFetcherInit<TInterceptorContext = unknown> = RequestInit & {
    fetch?: typeof fetch;
    skipInterceptor?: boolean;
    ctx?: TInterceptorContext;
};
type ClientRouteFetcher<TSchemaRoute extends SchemaRoute, TInterceptorContext> = {
    [M in keyof TSchemaRoute]: TSchemaRoute[M] extends SchemaEndpoint ? TSchemaRoute[M]["input"] extends null ? (init?: ClientFetcherInit<TInterceptorContext>) => Promise<TSchemaRoute[M]["output"] extends z.ZodType ? z.infer<TSchemaRoute[M]["output"]> : unknown> : TSchemaRoute[M]["input"] extends z.ZodType ? (input: z.input<TSchemaRoute[M]["input"]>, init?: ClientFetcherInit<TInterceptorContext>) => Promise<TSchemaRoute[M]["output"] extends z.ZodType ? z.infer<TSchemaRoute[M]["output"]> : unknown> : (input?: unknown, init?: ClientFetcherInit<TInterceptorContext>) => Promise<TSchemaRoute[M]["output"] extends z.ZodType ? z.infer<TSchemaRoute[M]["output"]> : unknown> : TSchemaRoute[M] extends SchemaRoute ? ClientRouteFetcher<TSchemaRoute[M], TInterceptorContext> : never;
};
type ClientFetcher<TSchema extends Schema, TInterceptorContext> = {
    [K in keyof TSchema]: TSchema[K] extends SchemaRoute ? ClientRouteFetcher<TSchema[K], TInterceptorContext> : TSchema[K] extends Schema ? ClientFetcher<TSchema[K], TInterceptorContext> : never;
};
type Client<TSchema extends Schema, TInterceptorContext = unknown> = ReturnType<typeof createClient<TSchema, TInterceptorContext>>;
declare class HttpError extends Error {
    method: string;
    url: string;
    status: number;
    description: string;
    constructor(method: string, url: string, status: number, description: string);
}
declare function createClient<TSchema extends Schema, TInterceptorContext = unknown>(schema: TSchema, options: {
    entrypointUrl: string;
    transformer?: Transformer;
    init?: RequestInit;
    fetch?: typeof fetch;
    interceptor?: (data: {
        method: Method;
        path: string;
        response: Response;
        ctx?: TInterceptorContext;
    }) => MaybePromise<void>;
}): {
    fetcher: ClientFetcher<TSchema, TInterceptorContext>;
};

export { type Client, type ClientFetcher, type ClientRouteFetcher, HttpError, createClient };
