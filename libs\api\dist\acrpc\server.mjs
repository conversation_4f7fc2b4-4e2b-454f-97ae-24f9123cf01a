import {
  dir,
  jsonTransformer,
  kebabTransformer,
  log
} from "../chunk-NKKQI6P3.mjs";
import "../chunk-J5LGTIGS.mjs";

// src/acrpc/server.ts
import { z } from "zod";
import express, { Router } from "express";
function isEndpoint(schemaEntry) {
  return schemaEntry != null && typeof schemaEntry === "object" && ("input" in schemaEntry && (schemaEntry["input"] instanceof z.ZodType || schemaEntry["input"] === null || schemaEntry["input"] === void 0) && ("output" in schemaEntry && (schemaEntry["output"] instanceof z.ZodType || schemaEntry["output"] === null || schemaEntry["output"] === void 0)));
}
function getInput(req, inputSchema, transformer) {
  let body = null;
  dir({
    method: req.method,
    query: req.query,
    body: req.body
  });
  if (req.method === "GET") {
    body = req.query.__body ? decodeURIComponent(req.query.__body) : null;
    log("get", { body });
    if (inputSchema && !body) {
      return {
        success: false,
        status: 400,
        response: {
          error: "No __body provided"
        }
      };
    }
  } else {
    body = req.body;
    log("non-get", { body });
    if (inputSchema && !body) {
      return {
        success: false,
        status: 400,
        response: {
          error: "No body provided"
        }
      };
    }
  }
  log("before deserialize", { body });
  const rawInput = body ? transformer.deserialize(body) : null;
  dir({ rawInput });
  if (inputSchema) {
    const schemaParseResult = inputSchema.safeParse(rawInput);
    dir({ schemaParseResult });
    if (schemaParseResult.success) {
      return {
        success: true,
        data: schemaParseResult.data
      };
    }
    return {
      success: false,
      status: 400,
      response: schemaParseResult.error.issues.map((issue) => ({
        path: issue.path,
        message: issue.message
      }))
    };
  }
  return {
    success: true,
    data: rawInput
  };
}
function createServer(schema, handlers, options) {
  const transformer = options?.transformer ?? jsonTransformer;
  const getMetadata = options?.getMetadata;
  const router = Router();
  const textMiddleware = express.text({ type: "*/*" });
  function fillRouter(schema2, handlers2, names) {
    log({ names });
    for (const [name, handler] of Object.entries(handlers2)) {
      log({ name });
      const schemaEntry = schema2[name];
      if (isEndpoint(schemaEntry)) {
        const path = ["", ...names].join("/");
        const method = name;
        const okStatus = method === "post" ? 201 : 200;
        log(`Registering ${method.toUpperCase()} ${path}...`);
        if (method !== "get") {
          router[method](path, textMiddleware);
        }
        router[method](
          path,
          async (req, res) => {
            let metadata = void 0;
            const isMetadataUsed = schemaEntry.isMetadataUsed ?? true;
            const isMetadataRequired = schemaEntry.isMetadataRequired ?? true;
            dir({
              getMetadata,
              isMetadataUsed,
              isMetadataRequired
            });
            if (isMetadataUsed) {
              metadata = getMetadata?.(req) ?? null;
              dir({ metadata });
              if (isMetadataRequired && !metadata) {
                return res.status(400).json({
                  error: "Metadata cannot be parsed."
                });
              }
            }
            const inputParseResult = getInput(
              req,
              schemaEntry.input,
              transformer
            );
            dir({ inputParseResult });
            if (!inputParseResult.success) {
              return res.status(inputParseResult.status).json(inputParseResult.response);
            }
            dir({ handlers: handlers2 });
            const rawOutput = await handler(
              inputParseResult.data,
              metadata ?? null,
              {
                req,
                res
              }
            );
            let output = null;
            if (schemaEntry.output !== null) {
              let parsedOutput = null;
              if (schemaEntry.output) {
                const outputParseResult = schemaEntry.output.safeParse(rawOutput);
                dir({
                  rawOutput,
                  outputParseResult
                });
                if (outputParseResult.error) {
                  return res.status(500).json(outputParseResult.error);
                }
                parsedOutput = outputParseResult.data;
              } else {
                parsedOutput = rawOutput;
              }
              const serializedOutput = transformer.serialize(parsedOutput);
              dir({ serializedOutput });
              output = serializedOutput;
            }
            if (schemaEntry.cacheControl) {
              res.setHeader("Cache-Control", schemaEntry.cacheControl);
            }
            res.status(okStatus);
            if (output != null) {
              res.send(output);
            } else {
              res.send("");
            }
          }
        );
      } else {
        fillRouter(
          schema2[name],
          handlers2[name],
          [...names, kebabTransformer.transform(name)]
        );
      }
    }
  }
  fillRouter(schema, handlers, []);
  function register(handlers2) {
    fillRouter(schema, handlers2, []);
  }
  return {
    router,
    register
  };
}
export {
  createServer
};
//# sourceMappingURL=server.mjs.map