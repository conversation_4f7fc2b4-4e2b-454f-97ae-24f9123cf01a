"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var ReactorHubService_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.ReactorHubService = void 0;
const common_1 = require("@nestjs/common");
const utils_1 = require("../utils");
const errors_1 = require("../common/errors");
const minio_service_1 = require("../minio/minio.service");
const prisma_service_1 = require("../prisma/prisma.service");
let ReactorHubService = ReactorHubService_1 = class ReactorHubService {
    constructor(prisma, minioService) {
        this.prisma = prisma;
        this.minioService = minioService;
        this.logger = new common_1.Logger(ReactorHubService_1.name);
    }
    async getHubs(input, user) {
        const { ids, query } = input;
        const hubs = await this.prisma.reactorHub.findMany({
            ...(0, utils_1.toPrismaPagination)(input.pagination),
            where: Object.assign({}, ids && { id: { in: ids } }, !user.isAdmin && { deletedAt: null }, query && {
                OR: [
                    {
                        id: query,
                    },
                    {
                        name: (0, utils_1.toPrismaLocalizationsWhere)(query),
                    },
                    {
                        description: (0, utils_1.toPrismaLocalizationsWhere)(query),
                    },
                ],
            }),
            select: {
                id: true,
                headUser: {
                    include: {
                        name: true,
                        image: true,
                    },
                },
                name: true,
                description: true,
                image: true,
                createdAt: true,
                updatedAt: true,
                deletedAt: user.isAdmin,
            },
        });
        return hubs;
    }
    async createHub(input, user) {
        if (!user.isAdmin) {
            throw new common_1.ForbiddenException(...(0, errors_1.getError)("must_be_admin"));
        }
        const hub = await this.prisma.reactorHub.create({
            data: {
                headUserId: input.headUserId,
                name: {
                    create: (0, utils_1.toPrismaLocalizations)(input.name, "name"),
                },
                description: {
                    create: (0, utils_1.toPrismaLocalizations)(input.description, "description"),
                },
            },
        });
        return hub;
    }
    async updateHub(input, user) {
        const hub = await this.prisma.reactorHub.findUniqueOrThrow({
            where: { id: input.id, deletedAt: null },
        });
        if (!user.isAdmin) {
            if (hub.headUserId !== user.id) {
                throw new common_1.ForbiddenException(...(0, errors_1.getError)("must_be_admin_or_head_user"));
            }
        }
        const { name, description } = input;
        await this.prisma.reactorHub.update({
            where: { id: input.id },
            data: {
                name: name && {
                    deleteMany: {},
                    create: (0, utils_1.toPrismaLocalizations)(name, "name"),
                },
                description: description && {
                    deleteMany: {},
                    create: (0, utils_1.toPrismaLocalizations)(description, "description"),
                },
            },
        });
    }
    async updateHubImage(id, file, user) {
        const hub = await this.prisma.reactorHub.findUniqueOrThrow({
            where: { id, deletedAt: null },
        });
        if (!user.isAdmin) {
            if (hub.headUserId !== user.id) {
                throw new common_1.ForbiddenException(...(0, errors_1.getError)("must_be_admin_or_head_user"));
            }
        }
        await this.prisma.$transaction(async (trx) => {
            const imageUrl = await this.minioService.uploadImage(file, "reactor-hub", id);
            const image = await trx.image.create({
                data: {
                    url: imageUrl,
                },
            });
            await trx.reactorHub.update({
                where: { id },
                data: {
                    imageId: image.id,
                },
            });
        });
    }
    async deleteHubImage(id, user) {
        const hub = await this.prisma.reactorHub.findUniqueOrThrow({
            where: { id, deletedAt: null },
            include: {
                image: true,
            },
        });
        if (!user.isAdmin) {
            if (hub.headUserId !== user.id) {
                throw new common_1.ForbiddenException(...(0, errors_1.getError)("must_be_admin_or_head_user"));
            }
        }
        const hubImage = hub.image;
        if (!hubImage) {
            return;
        }
        await this.prisma.$transaction(async (trx) => {
            await trx.reactorHub.update({
                where: { id },
                data: {
                    imageId: null,
                },
            });
            await trx.image.update({
                where: { id: hubImage.id },
                data: {
                    deletedAt: new Date(),
                },
            });
        });
    }
};
exports.ReactorHubService = ReactorHubService;
exports.ReactorHubService = ReactorHubService = ReactorHubService_1 = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [prisma_service_1.PrismaService,
        minio_service_1.MinioService])
], ReactorHubService);
//# sourceMappingURL=reactor-hub.service.js.map