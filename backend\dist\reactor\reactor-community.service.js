"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var ReactorCommunityService_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.ReactorCommunityService = void 0;
const common_1 = require("@nestjs/common");
const utils_1 = require("../utils");
const errors_1 = require("../common/errors");
const minio_service_1 = require("../minio/minio.service");
const prisma_service_1 = require("../prisma/prisma.service");
let ReactorCommunityService = ReactorCommunityService_1 = class ReactorCommunityService {
    constructor(prisma, minioService) {
        this.prisma = prisma;
        this.minioService = minioService;
        this.logger = new common_1.Logger(ReactorCommunityService_1.name);
    }
    async getCommunities(input, user) {
        const { ids, query, hubId } = input;
        const communities = await this.prisma.reactorCommunity.findMany({
            ...(0, utils_1.toPrismaPagination)(input.pagination),
            where: Object.assign({}, ids && { id: { in: ids } }, !user.isAdmin && { deletedAt: null }, hubId && { hubId }, query && {
                OR: [
                    {
                        id: query,
                    },
                    {
                        name: (0, utils_1.toPrismaLocalizationsWhere)(query),
                    },
                    {
                        description: (0, utils_1.toPrismaLocalizationsWhere)(query),
                    },
                ],
            }),
            select: {
                id: true,
                hub: {
                    include: {
                        name: true,
                        image: true,
                    },
                },
                headUser: {
                    include: {
                        name: true,
                        image: true,
                    },
                },
                name: true,
                description: true,
                image: true,
                createdAt: true,
                updatedAt: true,
                deletedAt: user.isAdmin,
            },
        });
        return communities;
    }
    async createCommunity(input, user) {
        if (!user.isAdmin) {
            throw new common_1.ForbiddenException(...(0, errors_1.getError)("must_be_admin"));
        }
        const community = await this.prisma.reactorCommunity.create({
            data: {
                headUserId: user.id,
                hubId: input.hubId,
                name: {
                    create: (0, utils_1.toPrismaLocalizations)(input.name, "name"),
                },
                description: {
                    create: (0, utils_1.toPrismaLocalizations)(input.description, "description"),
                },
            },
        });
        return community;
    }
    async updateCommunity(input, user) {
        const community = await this.prisma.reactorCommunity.findUniqueOrThrow({
            where: { id: input.id, deletedAt: null },
        });
        if (!user.isAdmin) {
            if (community.headUserId !== user.id) {
                throw new common_1.ForbiddenException(...(0, errors_1.getError)("must_be_admin_or_head_user"));
            }
        }
        const { name, description } = input;
        await this.prisma.reactorCommunity.update({
            where: { id: input.id },
            data: {
                name: name && {
                    deleteMany: {},
                    create: (0, utils_1.toPrismaLocalizations)(name, "name"),
                },
                description: description && {
                    deleteMany: {},
                    create: (0, utils_1.toPrismaLocalizations)(description, "description"),
                },
            },
        });
    }
    async updateCommunityImage(id, file, user) {
        const community = await this.prisma.reactorCommunity.findUniqueOrThrow({
            where: { id, deletedAt: null },
        });
        if (!user.isAdmin) {
            if (community.headUserId !== user.id) {
                throw new common_1.ForbiddenException(...(0, errors_1.getError)("must_be_admin_or_head_user"));
            }
        }
        await this.prisma.$transaction(async (trx) => {
            const imageUrl = await this.minioService.uploadImage(file, "reactor-community", id);
            const image = await trx.image.create({
                data: {
                    url: imageUrl,
                },
            });
            await trx.reactorCommunity.update({
                where: { id },
                data: {
                    imageId: image.id,
                },
            });
        });
    }
    async deleteCommunityImage(id, user) {
        const community = await this.prisma.reactorCommunity.findUniqueOrThrow({
            where: { id, deletedAt: null },
            include: {
                image: true,
            },
        });
        if (!user.isAdmin) {
            if (community.headUserId !== user.id) {
                throw new common_1.ForbiddenException(...(0, errors_1.getError)("must_be_admin_or_head_user"));
            }
        }
        const communityImage = community.image;
        if (!communityImage) {
            return;
        }
        await this.prisma.$transaction(async (trx) => {
            await trx.reactorCommunity.update({
                where: { id },
                data: {
                    imageId: null,
                },
            });
            await trx.image.update({
                where: { id: communityImage.id },
                data: {
                    deletedAt: new Date(),
                },
            });
        });
    }
};
exports.ReactorCommunityService = ReactorCommunityService;
exports.ReactorCommunityService = ReactorCommunityService = ReactorCommunityService_1 = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [prisma_service_1.PrismaService,
        minio_service_1.MinioService])
], ReactorCommunityService);
//# sourceMappingURL=reactor-community.service.js.map