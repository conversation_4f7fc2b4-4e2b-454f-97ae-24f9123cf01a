<script lang="ts">
  import type { Rating, Common } from "@commune/api";

  import { Consts } from "@commune/api";
  import { getClient } from "$lib/acrpc";
  import { getUserRateColor } from "$lib";
  import LocalizedTextarea from "../../../components/localized-textarea.svelte";

  const i18n = {
    en: {
      _page: {
        title: "Feedback — Commune",
      },
      userNotFound: "User not found",
      feedbackHistory: "Feedback History",
      sendFeedback: "Send Feedback",
      feedbackModalTitle: "Send Feedback",
      rating: "Rating",
      anonymous: "Anonymous",
      sendAnonymously: "Send anonymously",
      feedback: "Feedback",
      feedbackPlaceholder: "Enter your feedback...",
      cancel: "Cancel",
      submit: "Submit",
      submitting: "Submitting...",
      success: "Feedback sent successfully",
      errorSubmitting: "Error submitting feedback",
      noFeedbacks: "No feedback found",
      loadingMore: "Loading more...",
      errorOccurred: "An error occurred",
      errorFetchingFeedbacks: "Error fetching feedbacks",
      ratingRequired: "Rating is required",
      feedbackRequired: "Feedback text is required",
    },
    ru: {
      _page: {
        title: "Отзывы — Коммуна",
      },
      userNotFound: "Пользователь не найден",
      feedbackHistory: "История отзывов",
      sendFeedback: "Отправить отзыв",
      feedbackModalTitle: "Отправить отзыв",
      rating: "Оценка",
      anonymous: "Анонимно",
      sendAnonymously: "Отправить анонимно",
      feedback: "Отзыв",
      feedbackPlaceholder: "Введите ваш отзыв...",
      cancel: "Отмена",
      submit: "Отправить",
      submitting: "Отправка...",
      success: "Отзыв успешно отправлен",
      errorSubmitting: "Ошибка при отправке отзыва",
      noFeedbacks: "Отзывы не найдены",
      loadingMore: "Загрузка...",
      errorOccurred: "Произошла ошибка",
      errorFetchingFeedbacks: "Ошибка загрузки отзывов",
      ratingRequired: "Оценка обязательна",
      feedbackRequired: "Текст отзыва обязателен",
    },
  };

  const { fetcher: api } = getClient();

  const { data } = $props();
  const { me, user, locale, getAppropriateLocalization } = $derived(data);

  const t = $derived(i18n[locale]);

  // State using runes
  let feedbacks = $state(data.feedbacks);
  let error = $state<string | null>(null);

  let isLoadingMore = $state(false);
  let currentPage = $state(1);
  let isHasMoreFeedbacks = $state(data.isHasMoreFeedbacks);

  // Reference to the sentinel element for intersection observer
  let sentinelElement = $state<HTMLElement | null>(null);

  // Modal state
  let showModal = $state(false);
  let selectedRating = $state<number | null>(null);
  let isAnonymous = $state(false);
  let feedbackText = $state<Common.Localizations>([]);
  let isSubmitting = $state(false);
  let submitError = $state<string | null>(null);
  let submitSuccess = $state(false);

  // Function to load more feedbacks
  async function loadMoreFeedbacks() {
    if (isLoadingMore || !isHasMoreFeedbacks) return;

    isLoadingMore = true;
    error = null;

    try {
      const nextPage = currentPage + 1;

      const newFeedbacks = await api.rating.feedback.list.get({
        pagination: { page: nextPage },
        userId: user.id,
      });

      feedbacks = [...feedbacks, ...newFeedbacks];
      currentPage = nextPage;

      isHasMoreFeedbacks = newFeedbacks.length === Consts.PAGE_SIZE;
    } catch (err) {
      error = err instanceof Error ? err.message : t.errorOccurred;
      console.error(err);
    } finally {
      isLoadingMore = false;
    }
  }

  // Set up intersection observer for infinite scroll
  $effect(() => {
    if (!sentinelElement) return;

    const observer = new IntersectionObserver(
      (entries) => {
        if (entries[0].isIntersecting && isHasMoreFeedbacks && !isLoadingMore) {
          loadMoreFeedbacks();
        }
      },
      { threshold: 0.1 },
    );

    observer.observe(sentinelElement);

    return () => {
      observer.disconnect();
    };
  });

  // Handle keyboard events for modal
  $effect(() => {
    if (!showModal) return;

    const handleKeydown = (event: KeyboardEvent) => {
      if (event.key === "Escape") {
        closeModal();
      }
    };

    document.addEventListener("keydown", handleKeydown);

    return () => {
      document.removeEventListener("keydown", handleKeydown);
    };
  });

  // Derived values
  const userName = $derived(getAppropriateLocalization(user.name));

  // Modal functions
  const openModal = () => {
    showModal = true;
    selectedRating = null;
    isAnonymous = false;
    feedbackText = [];
    submitError = null;
    submitSuccess = false;
  };

  const closeModal = () => {
    showModal = false;
    selectedRating = null;
    isAnonymous = false;
    feedbackText = [];
    submitError = null;
    submitSuccess = false;
  };

  const handleSubmitFeedback = async () => {
    if (selectedRating === null) {
      submitError = t.ratingRequired;
      return;
    }

    if (feedbackText.length === 0 || !feedbackText.some((f) => f.value.trim())) {
      submitError = t.feedbackRequired;
      return;
    }

    isSubmitting = true;
    submitError = null;

    try {
      await api.rating.feedback.post({
        sourceUserId: me.id,
        targetUserId: user.id,
        value: selectedRating,
        isAnonymous,
        text: feedbackText,
      });

      submitSuccess = true;

      setTimeout(() => {
        refresh();
      }, 1500);
    } catch (err) {
      submitError = err instanceof Error ? err.message : t.errorSubmitting;
      console.error(err);
    } finally {
      isSubmitting = false;
    }
  };

  // Get author display name
  const getAuthorDisplayName = (author: Rating.GetUserFeedbacksOutput[0]["author"]) => {
    if (!author) return t.anonymous;

    return getAppropriateLocalization(author.name) || author.email;
  };

  // Generate star display for rating
  const getStarDisplay = (value: number) => {
    return "★".repeat(Math.floor(value / 2)) + (value % 2 === 1 ? "☆" : "");
  };

  function refresh() {
    window.location.reload();
  }
</script>

<svelte:head>
  <title>{userName} {t._page.title}</title>
</svelte:head>

<div class="container py-4">
  <div class="responsive-container">
    {#if !user}
      <div class="alert alert-danger" role="alert">
        {t.userNotFound}
      </div>
    {:else}
      <!-- Header with user name and action button -->
      <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
          <h2 class="mb-1">{userName}</h2>
          <p class="text-muted mb-0">{t.feedbackHistory}</p>
        </div>

        <!-- Action button in top right corner -->
        <div>
          <button class="btn btn-primary btn-sm" onclick={openModal} aria-label={t.sendFeedback}>
            <i class="bi bi-chat-dots me-1"></i>
            {t.sendFeedback}
          </button>
        </div>
      </div>

      <!-- Feedbacks list -->
      {#if feedbacks.length === 0}
        <div class="text-center py-5">
          <p class="text-muted">{t.noFeedbacks}</p>
        </div>
      {:else}
        {#each feedbacks as feedback (feedback.id)}
          <div class="card mb-3 shadow-sm">
            <div class="card-body">
              <div class="d-flex align-items-start">
                <!-- Author avatar -->
                <div class="me-3">
                  {#if feedback.author?.image}
                    <img
                      src={`/images/${feedback.author.image}`}
                      alt={getAuthorDisplayName(feedback.author)}
                      class="rounded-circle"
                      style="width: 48px; height: 48px; object-fit: cover;"
                    />
                  {:else}
                    <div
                      class="rounded-circle bg-secondary d-flex align-items-center justify-content-center text-white"
                      style="width: 48px; height: 48px;"
                    >
                      <i class="bi bi-person-fill"></i>
                    </div>
                  {/if}
                </div>

                <!-- Content -->
                <div class="flex-grow-1">
                  <div class="d-flex justify-content-between align-items-start mb-2">
                    <div>
                      <h6 class="mb-1">{getAuthorDisplayName(feedback.author)}</h6>
                    </div>
                    <div class="d-flex align-items-center gap-2">
                      <span
                        class="badge fs-6"
                        style="color: {getUserRateColor(
                          feedback.value,
                        )}; border: 1px solid {getUserRateColor(feedback.value)};"
                      >
                        {feedback.value}/10
                      </span>
                      <span style="font-size: 1.2rem; color: {getUserRateColor(feedback.value)};">
                        {getStarDisplay(feedback.value)}
                      </span>
                    </div>
                  </div>

                  <!-- Feedback text -->
                  <p class="mb-0 text-muted">
                    {getAppropriateLocalization(feedback.text)}
                  </p>
                </div>
              </div>
            </div>
          </div>
        {/each}
      {/if}

      <!-- Infinite scroll sentinel element -->
      {#if isHasMoreFeedbacks}
        <div bind:this={sentinelElement} class="text-center py-3">
          {#if isLoadingMore}
            <div class="spinner-border spinner-border-sm" role="status">
              <span class="visually-hidden">{t.loadingMore}</span>
            </div>
            <p class="text-muted mt-2 mb-0">{t.loadingMore}</p>
          {/if}
        </div>
      {/if}

      {#if error}
        <div class="alert alert-danger" role="alert">
          {error}
        </div>
      {/if}
    {/if}
  </div>

  <!-- Feedback Modal -->
  {#if showModal}
    <div
      class="modal fade show d-block"
      tabindex="-1"
      style="background-color: rgba(0,0,0,0.5);"
      role="dialog"
      aria-modal="true"
      onclick={(e) => e.target === e.currentTarget && closeModal()}
      onkeydown={(e) => e.key === "Enter" && e.target === e.currentTarget && closeModal()}
    >
      <div class="modal-dialog modal-lg">
        <div class="modal-content">
          <div class="modal-header">
            <h5 class="modal-title">{t.feedbackModalTitle}</h5>
            <button type="button" class="btn-close" onclick={closeModal} aria-label="Close"
            ></button>
          </div>
          <div class="modal-body">
            <!-- Rating selection -->
            <div class="mb-3">
              <div class="form-label">{t.rating}</div>
              <div class="rating-container">
                <div class="rating-stars d-flex gap-1 mb-2">
                  {#each Array(11) as _, i}
                    <button
                      type="button"
                      class="btn btn-outline-warning star-btn {selectedRating === i
                        ? 'active'
                        : ''}"
                      onclick={() => (selectedRating = i)}
                      disabled={isSubmitting}
                      aria-label={`Rate ${i} out of 10`}
                    >
                      {i}
                    </button>
                  {/each}
                </div>
              </div>
              {#if selectedRating !== null}
                <div class="text-muted small">
                  Selected rating: {selectedRating}/10
                  <span
                    style="font-size: 1.1rem; margin-left: 0.5rem; color: {getUserRateColor(
                      selectedRating,
                    )};"
                  >
                    {getStarDisplay(selectedRating)}
                  </span>
                </div>
              {/if}
            </div>

            <!-- Anonymous checkbox -->
            <div class="mb-3">
              <div class="form-check">
                <input
                  class="form-check-input"
                  type="checkbox"
                  id="anonymousCheck"
                  bind:checked={isAnonymous}
                  disabled={isSubmitting}
                />
                <label class="form-check-label" for="anonymousCheck">
                  {t.sendAnonymously}
                </label>
              </div>
            </div>

            <!-- Feedback textarea -->
            <LocalizedTextarea
              {locale}
              id="feedbackText"
              label={t.feedback}
              placeholder={t.feedbackPlaceholder}
              rows={4}
              required
              bind:value={feedbackText}
            />

            <!-- Error message -->
            {#if submitError}
              <div class="alert alert-danger" role="alert">
                {submitError}
              </div>
            {/if}

            <!-- Success message -->
            {#if submitSuccess}
              <div class="alert alert-success" role="alert">
                {t.success}
              </div>
            {/if}
          </div>
          <div class="modal-footer">
            <button
              type="button"
              class="btn btn-secondary"
              onclick={closeModal}
              disabled={isSubmitting}
            >
              {t.cancel}
            </button>
            <button
              type="button"
              class="btn btn-primary"
              onclick={handleSubmitFeedback}
              disabled={isSubmitting ||
                selectedRating === null ||
                feedbackText.length === 0 ||
                !feedbackText.some((f) => f.value.trim())}
            >
              {#if isSubmitting}
                <span class="spinner-border spinner-border-sm me-2" role="status"></span>
                {t.submitting}
              {:else}
                {t.submit}
              {/if}
            </button>
          </div>
        </div>
      </div>
    </div>
  {/if}
</div>

<style>
  .card {
    transition: transform 0.2s ease-in-out;
  }

  .card:hover {
    transform: translateY(-2px);
  }

  .star-btn {
    min-width: 40px;
    width: 40px;
    height: 40px;
    border-radius: 50%;
    font-weight: bold;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 0;
    font-size: 0.9rem;
  }

  .star-btn:hover {
    transform: scale(1.1);
  }

  .star-btn.active {
    background-color: #ffc107;
    border-color: #ffc107;
    color: #000;
  }

  .rating-container {
    overflow-x: auto;
    padding-bottom: 5px;
  }

  .rating-stars {
    flex-wrap: nowrap !important;
    width: max-content;
    min-width: 100%;
  }

  /* Ensure the container doesn't break on smaller screens */
  @media (max-width: 576px) {
    .star-btn {
      min-width: 32px;
      width: 32px;
      height: 32px;
      font-size: 0.75rem;
    }

    .rating-stars {
      gap: 1px;
    }
  }

  @media (max-width: 480px) {
    .star-btn {
      min-width: 28px;
      width: 28px;
      height: 28px;
      font-size: 0.7rem;
    }
  }
</style>
