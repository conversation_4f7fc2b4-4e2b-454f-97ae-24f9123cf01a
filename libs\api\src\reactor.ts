import type { Infer } from "./types";

import { z } from "zod";
import {
    id,
    searchIds,
    searchQuery,
    maybeImageUrl,
    createdAt,
    updatedAt,
    deletedAt,
    LocalizationsSchema,
    PaginationSchema,
} from "./common";
import { SimpleUserSchema } from "./user";
import { tagName } from "./tag";

// common

export type RatingType = Infer<typeof RatingTypeSchema>;
export const RatingTypeSchema = z.enum(["like", "dislike"]);

export type Rating = Infer<typeof RatingSchema>;
export const RatingSchema = z.object({
    likes: z.number().int().nonnegative(),
    dislikes: z.number().int().nonnegative(),
    status: RatingTypeSchema.nullable(),
});

export const hubName = LocalizationsSchema.min(1);
export const hubDescription = LocalizationsSchema.min(1);
export const hubImage = maybeImageUrl;

export const communityName = LocalizationsSchema.min(1);
export const communityDescription = LocalizationsSchema.min(1);
export const communityImage = maybeImageUrl;

// posts

export const postUsefulness = z.number().int().min(0).max(10);

export type PostUsefulness = Infer<typeof PostUsefulnessSchema>;
export const PostUsefulnessSchema = z.object({
    value: postUsefulness.nullable(),
    count: z.number().int().nonnegative(),
    totalValue: z.number().min(0).max(10).nullable(),
});

export const postTitle = LocalizationsSchema.min(1);
export const postBody = LocalizationsSchema.min(1);

export type GetPostOutput = Infer<typeof GetPostsOutputSchema>;
export const GetPostOutputSchema = z.object({
    id,

    hub: z
        .object({
            id,
            name: hubName,
            image: hubImage,
        })
        .nullable(),

    community: z
        .object({
            id,
            name: communityName,
            image: communityImage,
        })
        .nullable(),

    author: SimpleUserSchema,

    rating: RatingSchema,
    usefulness: PostUsefulnessSchema,

    title: postTitle,
    body: postBody,

    tags: z.array(
        z.object({
            id,
            name: tagName,
        }),
    ),

    createdAt,
    updatedAt,
    deletedAt: deletedAt.optional(),
});

export type GetPostsInput = Infer<typeof GetPostsInputSchema>;
export const GetPostsInputSchema = z.object({
    pagination: PaginationSchema,

    id: id.optional(),
    lensId: id.nullable(),
});

export type GetPostsOutput = Infer<typeof GetPostsOutputSchema>;
export const GetPostsOutputSchema = z.array(GetPostOutputSchema);

export type CreatePostInput = Infer<typeof CreatePostInputSchema>;
export const CreatePostInputSchema = z.object({
    hubId: id.nullable(),
    communityId: id.nullable(),
    title: postTitle,
    body: postBody,
    tagIds: z.array(id),
});

export type UpdatePostInput = Infer<typeof UpdatePostInputSchema>;
export const UpdatePostInputSchema = z
    .object({
        id,

        title: postTitle.optional(),
        body: postBody.optional(),
        tagIds: z.array(id).optional(),
    });

export type DeletePostInput = Infer<typeof DeletePostInputSchema>;
export const DeletePostInputSchema = z.object({
    id,

    reason: z.string().nonempty().nullable(),
});
    
export type UpdatePostRatingInput = Infer<typeof UpdatePostRatingInputSchema>;
export const UpdatePostRatingInputSchema = z.object({
    id,

    type: RatingTypeSchema,
});

export type UpdatePostRatingOutput = Infer<typeof UpdatePostRatingOutputSchema>;
export const UpdatePostRatingOutputSchema = RatingSchema;

export type UpdatePostUsefulnessInput = Infer<typeof UpdatePostUsefulnessInputSchema>;
export const UpdatePostUsefulnessInputSchema = z.object({
    id,

    value: postUsefulness.nullable(),
});

export type UpdatePostUsefulnessOutput = Infer<typeof UpdatePostUsefulnessOutputSchema>;
export const UpdatePostUsefulnessOutputSchema = PostUsefulnessSchema;

// comments

export type CommentEntityType = Infer<typeof CommentEntityTypeSchema>;
export const CommentEntityTypeSchema = z.enum(["post", "comment"]);

export const commentBody = LocalizationsSchema.min(1);

export type GetCommentsInput = Infer<typeof GetCommentsInputSchema>;
export const GetCommentsInputSchema = z.union([
    z.object({
        id,
        entityType: z.never().optional(),
        entityId: z.never().optional(),
    }),
    z.object({
        id: z.never().optional(),
        entityType: CommentEntityTypeSchema,
        entityId: id,
    }),
]);

export type GetCommentsOutput = Infer<typeof GetCommentsOutputSchema>;
export const GetCommentsOutputSchema = z.array(
    z.object({
        id,

        path: z.string().nonempty(),
    
        author: SimpleUserSchema.nullable(),
    
        isAnonymous: z.boolean(),
        anonimityReason: z.string().nonempty().nullable(),
    
        rating: RatingSchema,
    
        body: commentBody.nullable(),
    
        childrenCount: z.number().int().nonnegative(),
    
        deleteReason: z.string().nonempty().nullable(),

        createdAt,
        updatedAt,
        deletedAt,
    })
);

export type CreateCommentInput = Infer<typeof CreateCommentInputSchema>;
export const CreateCommentInputSchema = z.object({
    entityType: CommentEntityTypeSchema,
    entityId: id,

    body: commentBody,
});

export type UpdateCommentInput = Infer<typeof UpdateCommentInputSchema>;
export const UpdateCommentInputSchema = z
    .object({
        id,

        body: commentBody.optional(),
    });

export type DeleteCommentInput = Infer<typeof DeleteCommentInputSchema>;
export const DeleteCommentInputSchema = z.object({
    id,

    reason: z.string().nonempty().nullable(),
});

export type UpdateCommentRatingInput = Infer<typeof UpdateCommentRatingInputSchema>;
export const UpdateCommentRatingInputSchema = z.object({
    id,
    type: RatingTypeSchema,
});

export type UpdateCommentRatingOutput = Infer<typeof UpdateCommentRatingOutputSchema>;
export const UpdateCommentRatingOutputSchema = RatingSchema;

export type AnonimifyCommentInput = Infer<typeof AnonimifyCommentInputSchema>;
export const AnonimifyCommentInputSchema = z.object({
    id,
    reason: z.string().nonempty().nullable(),
});

// lenses

export const lensName = z.string().nonempty();
export const lensCode = z.string().nonempty();

export type CreateLensInput = Infer<typeof CreateLensInputSchema>;
export const CreateLensInputSchema = z.object({
    name: lensName,
    code: lensCode,
});

export type UpdateLensInput = Infer<typeof UpdateLensInputSchema>;
export const UpdateLensInputSchema = z
    .object({
        id,

        name: lensName.optional(),
        code: lensCode.optional(),
    });

// hubs

export type GetHubsInput = Infer<typeof GetHubsInputSchema>;
export const GetHubsInputSchema = z
    .object({
        pagination: PaginationSchema,

        ids: searchIds,
        query: searchQuery,
    })
    .partial();

export type GetHubsOutput = Infer<typeof GetHubsOutputSchema>;
export const GetHubsOutputSchema = z.array(
    z.object({
        id,

        headUser: SimpleUserSchema,

        image: hubImage,

        name: hubName,
        description: hubDescription,

        createdAt,
        updatedAt,
        deletedAt: deletedAt.optional(),
    }),
);

export type CreateHubInput = Infer<typeof CreateHubInputSchema>;
export const CreateHubInputSchema = z.object({
    headUserId: id.nullable(),

    name: hubName,
    description: hubDescription,
});

export type UpdateHubInput = Infer<typeof UpdateHubInputSchema>;
export const UpdateHubInputSchema = z
    .object({
        id,

        name: hubName.optional(),
        description: hubDescription.optional(),
    });

// communities

export type GetCommunitiesInput = Infer<typeof GetCommunitiesInputSchema>;
export const GetCommunitiesInputSchema = z
    .object({
        pagination: PaginationSchema,

        ids: searchIds,
        query: searchQuery,

        hubId: id,
    })
    .partial();

export type GetCommunitiesOutput = Infer<typeof GetCommunitiesOutputSchema>;
export const GetCommunitiesOutputSchema = z.array(
    z.object({
        id,

        hub: z
            .object({
                id,
                name: hubName,
                image: hubImage,
            })
            .nullable(),

        headUser: SimpleUserSchema,

        image: communityImage,

        name: communityName,
        description: communityDescription,

        createdAt,
        updatedAt,
        deletedAt: deletedAt.optional(),
    }),
);

export type CreateCommunityInput = Infer<typeof CreateCommunityInputSchema>;
export const CreateCommunityInputSchema = z.object({
    hubId: id.nullable(),
    headUserId: id.nullable(),

    name: communityName,
    description: communityDescription,
});

export type UpdateCommunityInput = Infer<typeof UpdateCommunityInputSchema>;
export const UpdateCommunityInputSchema = z
    .object({
        id,

        name: communityName.optional(),
        description: communityDescription.optional(),
    });
